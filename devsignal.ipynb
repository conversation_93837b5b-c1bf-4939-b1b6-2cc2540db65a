import copy

def update_level_and_status_to_row_number(name, timeframe, row_number):
    global df, waves, recent_level, primary_level, trend_status, old_trend_status, old_label, old_stack_level, sub_primary_level
    if row_number is None:
        return

    waves[name][timeframe] = []
    trend_status[name][timeframe] = []
    old_trend_status[name][timeframe] = []

    # Init level start first candle and check to whole df
    if df[name][timeframe].iloc[0]['Open'] <= df[name][timeframe].iloc[0]['Close']:
        label = 1.0
    else:
        label = 0.0

    first_row = df[name][timeframe].iloc[0]
    df[name][timeframe].loc[first_row.name, 'ma_candle'] = df[name][timeframe].loc[first_row.name, 'candle_size']

    recent_level[name][timeframe] = {
        'secondary_key_index': first_row.name,
        'key_level_index': first_row.name,
        'start_index': first_row.name,
        'checked_index': first_row.name,
        'peak_index': first_row.name,
        'label': label,
        'type_wave': 'wave',
        'stack_level': 0,
        'start_stack_level': 0,
        'false_break_level_index': None,
        'false_break_peak_index': None,
        'old_peak_index': None,
        'old_key_level_index': None,
    }
    primary_level[name][timeframe] = copy.deepcopy(recent_level[name][timeframe])
    sub_primary_level[name][timeframe] = copy.deepcopy(recent_level[name][timeframe])
    latest_wave = {
        'start_index': recent_level[name][timeframe]['start_index'],
        'checked_index': recent_level[name][timeframe]['checked_index'],
        'peak_index': recent_level[name][timeframe]['peak_index'],
        'confirmed': True,
        'label': recent_level[name][timeframe]['label'],
        'ranges': [],
    }
    waves[name][timeframe].append(latest_wave)
    # old_trend_status[name][timeframe].append(copy.deepcopy(primary_level[name][timeframe]))
    # trend_status[name][timeframe].append(copy.deepcopy(primary_level[name][timeframe]))

    new_index = 0
    row = df[name][timeframe].iloc[new_index]
    update_trend_status(df[name][timeframe], waves[name][timeframe], primary_level[name][timeframe], name, timeframe)
    while new_index < row_number:
        update_level_and_trend_status_with_new_row(name, timeframe, row)
        index = df[name][timeframe].index.get_loc(row.name)
        print(f"--Index row {new_index} -- {name} {timeframe}")
        new_index = index + 1
        row = df[name][timeframe].iloc[new_index]
    return df[name][timeframe].iloc[new_index - 1]

import pandas as pd
import mplfinance as mpf
import numpy as np
from datetime import datetime

# df = pd.read_csv('eu_15.csv', index_col='Time', parse_dates=True)
# df['candle_size'] = abs(df['Open'] - df['Close'])
# df['ma_candle'] = None
history = {
    'primary_level': [],
    'recent_level': [],
    'sub_primary_level': [],
}
recent_level = {}
primary_level = {}
sub_primary_level = {}
waves = {}
past_ranges = {}
primary_waves = {}
position_status = {}
pending_position = {}
closed_position = {}
cancelled_position = {}
trend_status = {}
old_trend_status = {}
number_cal_ma_candle = 49
minimum_imbalance_ratio = 1
big_candle_ratio = 2.2
eg_candle_ratio = 80.0
doji_candle_ratio = 11.0
number_candles_sideway = 5
min_rr = 1
min_point_open = 4
ratio_ma_tp = 1
ratio_ma_sl = 3
small_candle_ratio = 1/3
percent_default_tp = 0.5
min_rr_open_now = 2.5
fake_breakout_checking = False
break_index = None
main_trend_reverse_checking = False
space_break_ratio = 0.1 # For update waves
level_break_space_ratio = 0.5
max_confirm_break_ratio = 11
weak_trend_ratio = 0.2
liquidity_ratio = 1
# For stra
waiting_stack_level_confirm = False
is_trendline_broken = False
broken_trendline = None
# BOS stra
bos_signal = False
lowest_point = None
bos_index = None
bos_checked_index = None
# trend reverse
old_label = {}
old_stack_level = {}

# Max row df
max_row_df = 100000
# Init data

df = {}
timeframes = [1, 5, 15, 60, 1440]
check_symbols = {
    'BTCUSD': None,
    # 'XAUUSD': 41,
}
for name, id in check_symbols.items():
    df[name] = {}
    recent_level[name] = {}
    primary_level[name] = {}
    sub_primary_level[name] = {}
    trend_status[name] = {}
    position_status[name] = {}
    pending_position[name] = {}
    old_trend_status[name] = {}
    closed_position[name] = {}
    cancelled_position[name] = {}
    waves[name] = {}
    past_ranges[name] = {}
    old_label[name] = {}
    old_stack_level[name] = {}

    # Handle timefram 1 minute

    for timeframe in timeframes:

        if timeframe == 1:
            file_name = f"data/{name}_{timeframe}.csv"
            df[name][timeframe] = pd.read_csv(file_name, sep=',', parse_dates=['Open time', 'Close time'], index_col='Open time')

            df[name][timeframe] = df[name][timeframe].tail(max_row_df)
        else:
            df[name][timeframe] = df[name][1].resample(f'{timeframe}min').agg({
                'Open': 'first',
                'High': 'max',
                'Low': 'min',
                'Close': 'last',
                'Volume': 'sum',
            })

        df[name][timeframe]['candle_size'] = abs(df[name][timeframe]['Open'] - df[name][timeframe]['Close'])
        df[name][timeframe]['Labels'] = (df[name][timeframe]['Close'] > df[name][timeframe]['Open']).astype(int)
        df[name][timeframe]['label'] = df[name][timeframe]['Labels']
        df[name][timeframe]['ma_candle'] = None
        df[name][timeframe]['span_a'] = float('nan')
        df[name][timeframe]['span_b'] = float('nan')
        df[name][timeframe]['tenkan'] = float('nan')
        df[name][timeframe]['kijun '] = float('nan')
        df[name][timeframe]['chikou'] = float('nan')
        recent_level[name][timeframe] = None
        primary_level[name][timeframe] = None
        sub_primary_level[name][timeframe] = None
        waves[name][timeframe] = []
        past_ranges[name][timeframe] = []
        trend_status[name][timeframe] = []
        old_trend_status[name][timeframe] = []
        old_label[name][timeframe] = []
        old_stack_level[name][timeframe] = []
        position_status[name][timeframe] = []
        pending_position[name][timeframe] = []
        closed_position[name][timeframe] = []
        cancelled_position[name][timeframe] = []



"Open time,Open,High,Low,Close,Volume,Close time,Quote asset volume,Number of trades,Taker buy base asset volume,Taker buy quote asset volume,Ignore"

df[name][1440].head(2)


def is_any_candle_outside(check_candles, label):
    first_candle = check_candles.iloc[0]
    other_candles = check_candles.iloc[1:]
    if label == 0: # if wave down
        return (other_candles['High'] < first_candle['Low']).any()
    else: # wave up
        return (other_candles['Low'] > first_candle['High']).any()

def is_candle_complete_inside(first_candle, check_candle):
    return check_candle['High'] <= first_candle['High'] and check_candle['Low'] >= first_candle['Low']

def is_break_with_space(checked_candles, label, break_ratio=None):
    global space_break_ratio
    break_ratio = break_ratio or space_break_ratio
    if label == 1:
        direction = 1
        peak = 'High'
    else:
        direction = -1
        peak = 'Low'
    high = checked_candles.iloc[0][peak]
    higher_space = None
    for local_index, row in checked_candles.iloc[1:].iterrows():
        if higher_space is not None:
            if (row['Close'] - high)*direction > row['ma_candle']*break_ratio:
                return True
            else:
                high = higher_space
                higher_space = None
        if (row['Close'] - high)*direction > 0:
            higher_space = row['Close']
    return False

def is_eg_candle(row):
    return abs(row['Open']-row['Close'])*100.0/abs(row['High']-row['Low']) >= eg_candle_ratio and size_candle(row) > row['ma_candle']

def is_doji_candle(row):
    return abs(row['Open']-row['Close'])*100.0/abs(row['High']-row['Low']) <= doji_candle_ratio

def size_candle(row):
    return abs(row['High'] - row['Low'])

def check_imbalance_3_candles(first_candle, second_candle, third_candle, label):
    if is_eg_candle(second_candle) == False or is_big_candle(second_candle) == False:
        return False
    if label == 0:
        if third_candle['Close'] > third_candle['Open'] or second_candle['Close'] > second_candle['Open']:
            return False
        if first_candle['Low'] <= third_candle['High']:
            return False
    else: # key_level['label'] == 1
        if third_candle['Close'] < third_candle['Open'] or second_candle['Close'] < second_candle['Open']:
            return False
        if first_candle['High'] >= third_candle['Low']:
            return False
    return first_candle.name

def has_imbalance(df, start_index, end_index, label):
    i = df.index.get_loc(start_index)
    peak_i = df.index.get_loc(end_index)
    if len(df.iloc[i:peak_i+1]) < 3:
        return False
    while i + 2 <= peak_i:
        first_candle = df.iloc[i]
        second_candle = df.iloc[i+1]
        third_candle = df.iloc[i+2]
        i = i + 1
        if second_candle['candle_size'] < second_candle['ma_candle'] * minimum_imbalance_ratio:
            continue
        if label == 0:
            if third_candle['Close'] > third_candle['Open'] or second_candle['Close'] > second_candle['Open']:
                continue
            if first_candle['Low'] <= third_candle['High']:
                continue
        else: # key_level['label'] == 1
            if third_candle['Close'] < third_candle['Open'] or second_candle['Close'] < second_candle['Open']:
                continue
            if first_candle['High'] >= third_candle['Low']:
                continue
        return first_candle.name
    return False

def is_trend_has_imbalance(df, trend_key):
    return has_imbalance(df, trend_key['key_level_index'], trend_key['peak_index'], trend_key['label'])

def has_big_candle(start_index, end_index):
    i = start_index
    peak_i = end_index
    for _, candle in df.iloc[i:peak_i+1].iterrows():
        if is_eg_candle(candle) and candle['candle_size'] >= candle['ma_candle']*big_candle_ratio:
            return True
    return False

def is_big_candle(candle):
    return candle['candle_size'] >= candle['ma_candle']*big_candle_ratio

def mean_candle(candle):
    return (candle['Open'] + candle['Close'])/2.0

def is_confirm_break(df, row_index, price, label, start_index, is_primary=True):
    global max_confirm_break_ratio, level_break_space_ratio
    break_candle_index = find_break_candle(df, row_index, price, label, False, start_index)
    if break_candle_index is None or break_candle_index >= row_index:
        return False
    if label == 1:
        low = 'Low'
        high = 'High'
        direction = 1
    else:
        low = 'High'
        high = 'Low'
        direction = -1

    # Price going too far so obvious break
    if (df.loc[break_candle_index]['Close'] - price) > max_confirm_break_ratio * df.loc[break_candle_index]['ma_candle']:
        print('Break confirmed bypass react')
        return True

    next_candle_index = df.iloc[df.index.get_loc(break_candle_index)+1].name
    if next_candle_index > row_index:
        return False
    try:
        next_next_candle_index = df.iloc[df.index.get_loc(next_candle_index)+1].name
    except:
        next_next_candle_index = None
    else:
        if next_next_candle_index > row_index:
            next_next_candle_index = None
    # if break candle is EG candle and the next candle size is smaller or equal to 1/3 break candle
    if is_eg_candle(df.loc[break_candle_index]) and size_candle(df.loc[next_candle_index])/size_candle(df.loc[break_candle_index]) <= small_candle_ratio \
        and (df.loc[next_candle_index][low] - price)*direction > 0:
        print('Break with EG candle and small confirm')
        return True
    if is_eg_candle(df.loc[break_candle_index]) and is_eg_candle(df.loc[next_candle_index]) and df.loc[next_candle_index]['Labels'] == label\
        and (df.loc[next_candle_index][low] - price)*direction > 0:
        print('Break with 2 EG candle')
        return True
    if is_big_candle(df.loc[break_candle_index]) and (df.loc[next_candle_index]['Close'] - price)*direction > level_break_space_ratio*df.loc[next_candle_index]['ma_candle'] and size_candle(df.loc[next_candle_index])/size_candle(df.loc[break_candle_index]) <= small_candle_ratio:
        print('Break with big candle and second candle close above and small')
        return True
    if is_break_with_space(df.loc[break_candle_index:row_index], label, level_break_space_ratio):
        print('Break with space')
        return True
    if  next_next_candle_index and (df.loc[next_candle_index][low] - price)*direction > level_break_space_ratio*df.loc[next_candle_index]['ma_candle'] and (df.loc[next_next_candle_index][low] - price)*direction > level_break_space_ratio*df.loc[next_candle_index]['ma_candle']:
        print('Break with candle count')
        return True
    fake_price_zone = df.loc[break_candle_index][high]
    return is_confirm_break(df, row_index, fake_price_zone, label, next_candle_index, is_primary)


def find_break_candle(df, check_index, price, label, last=True, start_index=None):
    if last:
        if label == 1:
            open = 'Open'
            close = 'Close'
            peak = 'High'
            direction = -1
            max_high = df.loc[df.iloc[0].name:check_index]['High'].max()
            if price > max_high:
                return None
        else:
            open = 'Close'
            close = 'Open'
            peak = 'Low'
            direction = 1
            min_low = df.loc[df.iloc[0].name:check_index]['Low'].min()
            if price < min_low:
                return None
        while True:
            if price >= df.loc[check_index][open] and price <= df.loc[check_index][close] or (df.loc[check_index][peak] - price)*direction > 0:
                return check_index
            if df.index.get_loc(check_index) == 0:
                return None
            check_index = df.iloc[df.index.get_loc(check_index) - 1].name
            if check_index < df.iloc[0].name:
                return None
    else:
        if start_index is None:
            print('Need start index to find first break candle')
            raise ValueError('Need start index to find break candle')

        if label == 1:
            open = 'Open'
            close = 'Close'
            peak = 'Low'
            direction = 1
            max_high = df.loc[start_index:check_index]['Close'].max()
            if price > max_high:
                return None
            else:
                local_df = df.loc[start_index:check_index]
                local_df = local_df.loc[local_df['Close'] > price]
                return local_df.iloc[0].name if len(local_df) > 0 else None
        else:
            open = 'Close'
            close = 'Open'
            peak = 'High'
            direction = -1
            min_low = df.loc[start_index:check_index]['Close'].min()
            if price < min_low:
                return None
            else:
                local_df = df.loc[start_index:check_index]
                local_df = local_df.loc[local_df['Close'] < price]
                return local_df.iloc[0].name if len(local_df) > 0 else None

def count_waves_from_peak_index(check_index, waves, peak_label, count_unconfirm=False):
    for index, wave in enumerate(reversed(waves)):
        if check_index >= wave['start_index'] and check_index <= wave['peak_index']:
            result = index
            if waves[-1]['confirmed'] is False and count_unconfirm == False:
                result = result - 1
            if peak_label != wave['label']:
                result = result + 1
            if result == -1:
                return 0
            return result
    return 0



def close_out_body_candle(candle, row_check):
    return row_check['Close'] > candle['Open'] and row_check['Close'] > candle['Close'] or \
        row_check['Close'] < candle['Open'] and row_check['Close'] < candle['Close']

def last_candle_label_same_wave(df, wave):
    filtered_df = df.loc[wave['start_index']:wave['checked_index']].loc[df['label'] == wave['label']]
    if len(filtered_df) > 0:
        return filtered_df.iloc[-1]
    return df.loc[wave['start_index']]

def update_waves_with_new_row_df(df, waves, new_row_df):
    index = df.index.get_loc(new_row_df.name)
    print(f"New Index call update waves: {index}")
    previous_index = df.index.get_loc(waves[-1]['checked_index'])
    if index <= previous_index:
        print('This row df already updated')
        return recent_level
    # Update indicator ichimoku
    last_26 = max(index - 26, 0)
    # Calculate Tenkan-sen (Conversion Line): (9-period high + 9-period low)/2
    df.loc[new_row_df.name, 'tenkan'] = (df.loc[:new_row_df.name].tail(9)['High'].max() + df.loc[:new_row_df.name].tail(9)['Low'].min())/2.0

    # Calculate Kijun-sen (Base Line): (26-period high + 26-period low)/2
    df.loc[new_row_df.name, 'kijun'] = (df.loc[:new_row_df.name].tail(26)['High'].max() + df.loc[:new_row_df.name].tail(26)['Low'].min())/2.0

    # Calculate Chikou Span (Lagging Span): Current closing price plotted 26 periods back
    df.loc[df.iloc[last_26].name, 'chikou'] = df.loc[new_row_df.name]['Close']

    # Calculate Senkou Span A (Leading Span A): (Tenkan-sen + Kijun-sen)/2 plotted 26 periods ahead
    # For current calculations, we need to use current Tenkan and Kijun values
    df.loc[new_row_df.name, 'span_a'] = (df.loc[new_row_df.name]['tenkan'] + df.loc[new_row_df.name]['kijun'])/2.0

    # Calculate Senkou Span B (Leading Span B): (52-period high + 52-period low)/2 plotted 26 periods ahead
    # Use current 52-period data, not data from 26 periods ago
    df.loc[new_row_df.name, 'span_b'] = (df.loc[:new_row_df.name].tail(52)['High'].max() + df.loc[:new_row_df.name].tail(52)['Low'].min())/2.0

    df.loc[new_row_df.name, 'candle_size'] = abs(df.loc[new_row_df.name]['Open'] - df.loc[new_row_df.name]['Close'])
    # df.loc[new_row_df.name, 'effort'] = df.loc[new_row_df.name, 'Volume']/df.loc[new_row_df.name, 'candle_size']
    # Calculate ma candle size
    if index-number_cal_ma_candle < 0:
        df.loc[new_row_df.name, 'ma_candle'] = df.loc[new_row_df.name, 'candle_size']
    else:
        df.loc[new_row_df.name, 'ma_candle'] = df.iloc[index-number_cal_ma_candle:index+1]['candle_size'].sum()/number_cal_ma_candle

    # Check if new candle going same direction with curent wave or close in body previous candle
    direction = 1 if waves[-1]['label'] else -1
    if (new_row_df['Close'] - df.loc[waves[-1]['checked_index'], 'Close'])*direction >= 0 or close_out_body_candle(last_candle_label_same_wave(df, waves[-1]), new_row_df) == False:
        print(f"New row df going same direction with current wave label is {new_row_df['Labels']} or close in body previous")
        # Extend wave
        waves[-1]['checked_index'] = new_row_df.name
        # Check new peak current wave
        if waves[-1]['label'] == 1:
            if df.loc[waves[-1]['peak_index']]['High'] < new_row_df['High']:
                waves[-1]['peak_index'] = new_row_df.name
        else:
            if df.loc[waves[-1]['peak_index']]['Low'] > new_row_df['Low']:
                waves[-1]['peak_index'] = new_row_df.name

        # Check confirm
        if waves[-1]['confirmed'] == False:
            # Check if have at least two candles and not inside opposite type bar
            if waves[-1]['peak_index'] > waves[-2]['peak_index'] and is_any_candle_outside(df.loc[waves[-2]['peak_index']:waves[-1]['checked_index']], waves[-1]['label']):
                # Confirm
                waves[-1]['confirmed'] = True
                print('Current wave now just confirm')
                waves[-1]['ranges'].append({
                    'start_index': waves[-1]['start_index'],
                    'end_index': df.iloc[previous_index].name,
                    'count': index - df.index.get_loc(waves[-1]['start_index'])
                })
                waves[-1]['start_index'] = waves[-2]['peak_index']
                if waves[-1]['label']:
                    waves[-1]['peak_index'] = df.loc[waves[-1]['start_index']:new_row_df.name]['High'].idxmax()
                else:
                    waves[-1]['peak_index'] = df.loc[waves[-1]['start_index']:new_row_df.name]['Low'].idxmin()
                waves[-2]['checked_index'] = waves[-2]['peak_index']
            else:
                # Not Confirm
                print('Current wave still not confirm, waiting for new row')
                if waves[-2]['label'] == 1:
                    if df.loc[waves[-2]['peak_index']]['High'] < new_row_df['High']:
                        waves[-2]['peak_index'] = new_row_df.name
                else:
                    if df.loc[waves[-2]['peak_index']]['Low'] > new_row_df['Low']:
                        waves[-2]['peak_index'] = new_row_df.name
    else: # Different direction with current wave
        print('New row direction different with current wave')
        # Check current wave confirm?

        if waves[-1]['confirmed']:
            if waves[-1]['label'] == 1:
                if df.loc[waves[-1]['peak_index']]['High'] < new_row_df['High']:
                    waves[-1]['peak_index'] = new_row_df.name
            else:
                if df.loc[waves[-1]['peak_index']]['Low'] > new_row_df['Low']:
                    waves[-1]['peak_index'] = new_row_df.name

            print('Make new unconfirmed wave append')
            # Spawn new waves with confirm false
            latest_wave = {
                'start_index': new_row_df.name,
                'checked_index': new_row_df.name,
                'peak_index': new_row_df.name,
                'confirmed': False,
                'label': 1 - waves[-1]['label'],
                'ranges': [],
            }
            # if waves[-1]['label'] == 1:
            #     if df.loc[waves[-1]['peak_index']]['High'] < new_row_df['High']:
            #         waves[-1]['peak_index'] = new_row_df.name
            # else:
            #     if df.loc[waves[-1]['peak_index']]['Low'] > new_row_df['Low']:
            #         waves[-1]['peak_index'] = new_row_df.name
            waves.append(latest_wave)
        else: # Current wave not confirm
            print('Current wave was not confirmed then new row different direction occurs')
            # Check if false confirm reverse
            if is_false_confirm_reverse(df, waves, new_row_df):
                print('False confirm reverse break, so extend previous wave to current wave + new row df')
                # Extend previous wave to current wave and new_row_df
                # Check new peak current wave again
                waves[-2]['peak_index'] = new_row_df.name
                waves[-2]['checked_index'] = new_row_df.name
                waves[-2]['ranges'].append({
                    'start_index': waves[-1]['start_index'],
                    'end_index': df.iloc[previous_index].name,
                    'count': index - df.index.get_loc(waves[-1]['start_index'])
                })
                waves.pop()
                if len(waves) > 1:
                    if waves[-1]['label']:
                        id_min = df.loc[waves[-1]['start_index']:new_row_df.name, 'Low'].idxmin()
                        if df.loc[waves[-2]['peak_index']]['Low'] > df.loc[id_min]['Low']:
                            waves[-2]['peak_index'] = id_min
                            waves[-2]['checked_index'] = id_min
                            waves[-1]['start_index'] = id_min
                    else:
                        id_max = df.loc[waves[-1]['start_index']:new_row_df.name, 'High'].idxmax()
                        if df.loc[waves[-2]['peak_index']]['High'] < df.loc[id_max]['High']:
                            waves[-2]['peak_index'] = id_max
                            waves[-2]['checked_index'] = id_max
                            waves[-1]['start_index'] = id_max

            else:
                # Check new peak current wave again
                if waves[-1]['label'] == 1:
                    if df.loc[waves[-1]['peak_index']]['High'] < new_row_df['High']:
                        waves[-1]['peak_index'] = new_row_df.name
                else:
                    if df.loc[waves[-1]['peak_index']]['Low'] > new_row_df['Low']:
                        waves[-1]['peak_index'] = new_row_df.name

                if waves[-2]['label'] == 1:
                    if df.loc[waves[-2]['peak_index']]['High'] < new_row_df['High']:
                        waves[-2]['peak_index'] = new_row_df.name
                else:
                    if df.loc[waves[-2]['peak_index']]['Low'] > new_row_df['Low']:
                        waves[-2]['peak_index'] = new_row_df.name

            waves[-1]['checked_index'] = new_row_df.name

def is_false_confirm_reverse(df, waves, new_row):
    if len(waves) < 2:
        return False
    if waves[-2]['label']:
        direction = 1
        peak = 'High'
    else:
        direction = -1
        peak = 'Low'
    return (new_row['Close'] - df.loc[waves[-2]['peak_index'], peak])*direction > 0

def find_smc_ob(df, key_level):
    # Find candles in 68 ratio
    if key_level['label']:
        high = 'High'
        low = 'Low'
        direction = 1
    else:
        high = 'Low'
        low = 'High'
        direction = -1
    range_price = abs(df.loc[key_level['peak_index'], high] - df.loc[key_level['key_level_index'], low])
    max_price = df.loc[key_level['peak_index'], high] - range_price*0.618*direction
    min_price = df.loc[key_level['peak_index'], high] - range_price*0.8*direction
    candles = df.loc[key_level['key_level_index']:key_level['peak_index']]
    ob_list = []
    # Get first candle potienal to be ob
    for i, candle in candles.iterrows():
        if (candle['Low'] >= min_price and candle['Low'] <= max_price) or (candle['High'] >= min_price and candle['High'] <= max_price) or (candle['Close'] >= min_price and candle['Close'] <= max_price):
            for ob in ob_list:
                # Check new ob test old ob
                if (candle[low] - df.loc[ob['ob_index']][high])*direction < 0:
                    ob_list.remove(ob)
            if is_doji_candle(candle) or candle['label'] != key_level['label']:
                df_candles = candles.loc[candle.name:].iloc[1:]
                end_index = df_candles.loc[df_candles['label'] != key_level['label']]
                if len(end_index) > 0:
                    end_index = end_index.iloc[0].name
                else:
                    end_index = None
                df_candles = df.loc[candle.name:end_index].iloc[0:3]
                if has_imbalance(df, df_candles.iloc[0].name, df_candles.iloc[-1].name, key_level['label']):
                    ob_list.append({
                        'ob_index': candle.name,
                        'strength': 'strong' if is_eg_candle(candle) else 'weak',
                    })
                    continue
    return ob_list

def level_from_waves(df, waves, primary_key=False, init_level=None):
    if len(waves) == 0:
        raise ValueError("waves is empty")
    index = 1
    if init_level:
        recent_level = init_level
        if recent_level['peak_index'] < waves[0]['peak_index']:
            recent_level['peak_index'] = waves[0]['peak_index']
        if recent_level['checked_index'] < waves[0]['checked_index']:
            recent_level['checked_index'] = waves[0]['checked_index']
        # filter checked_index
        if waves[-1]['checked_index'] > recent_level['checked_index'] and len(waves) > 4:
            index_latest_wave = find_wave_index_candle_belongs_to(recent_level['checked_index'], waves)
            if index_latest_wave and index_latest_wave > 1 - len(waves):
                index = max(index_latest_wave + len(waves) - 1, 1)
    else:
        recent_level = {
            'secondary_key_index': waves[0]['start_index'],
            'key_level_index': waves[0]['start_index'],
            'start_index': waves[0]['start_index'],
            'checked_index': waves[0]['checked_index'],
            'peak_index': waves[0]['peak_index'],
            'label': waves[0]['label'],
            'type_wave': 'wave',
            'stack_level': 0,
            'start_stack_level': 0,
            'false_break_level_index': None,
            'false_break_peak_index': None,
            'old_peak_index': None,
            'old_key_level_index': None,
        }
    while index < len(waves):
        if waves[index]['confirmed'] == False:
            recent_level['checked_index'] = waves[index]['checked_index']
            return recent_level
        if recent_level['label'] == 1:
            peak = 'Low'
            opposite_peak = 'High'
            direction = 1
            compare_func = min
        else:
            peak = 'High'
            opposite_peak = 'Low'
            direction = -1
            compare_func = max
        if waves[index]['label'] != recent_level['label']:
            if recent_level.get('ob_correction_index') is None:
                recent_level['ob_correction_index'] = waves[index]['peak_index']
                recent_level['ratio_trend'] = cal_ratio_trend(df, waves[index]['peak_index'], recent_level)
            else:
                if (df.loc[waves[index]['peak_index'], peak] - df.loc[recent_level['ob_correction_index'], peak])*direction < 0:
                    recent_level['ob_correction_index'] = waves[index]['peak_index']
                    recent_level['ratio_trend'] = cal_ratio_trend(df, waves[index]['peak_index'], recent_level)
            price_check = df.loc[recent_level['false_break_level_index']][peak] if recent_level['false_break_level_index'] else df.loc[recent_level['key_level_index']][peak]

            lookback_index = max(0, df.index.get_loc(waves[index]['checked_index']) - 26)
            displaced_span_b = df.iloc[lookback_index]['span_b']
            price_check = compare_func(price_check, displaced_span_b)
            expanded_checked_index = waves[index+1]['checked_index'] if index < len(waves)-1 else waves[index]['checked_index']
            # Compare with current key level
            if (price_check - df.loc[waves[index]['peak_index']][peak])*direction > 0:
                if waves[index]['confirmed'] and recent_level['key_level_index'] < waves[index]['start_index'] and ((df.loc[recent_level['key_level_index']][peak] - df.loc[waves[index]['start_index']]['Close'])*direction > 0 or is_confirm_break(df, expanded_checked_index, price_check, waves[index]['label'], recent_level['false_break_level_index'] or recent_level['key_level_index'], primary_key)):
                    number_waves = count_waves_from_peak_index(recent_level['peak_index'], waves[0:index+1], recent_level['label'])
                    if number_waves < 3:
                        recent_level = {
                            'secondary_key_index': recent_level['key_level_index'],
                            'key_level_index': recent_level['peak_index'],
                            'start_index': recent_level['key_level_index'],
                            'checked_index': waves[index]['checked_index'],
                            'peak_index': waves[index]['peak_index'],
                            'label': waves[index]['label'],
                            'type_wave': 'wave',
                            'stack_level': 0,
                            'start_stack_level': 0,
                            'false_break_level_index': None,
                            'false_break_peak_index': None,
                            'old_peak_index': None,
                            'old_key_level_index': None,
                            'old_stack_level': recent_level['stack_level'],
                        }
                    else:
                        if primary_key:
                            if recent_level['label']:
                                start_index = df.loc[recent_level['peak_index']:waves[index]['peak_index']].iloc[1:][peak].idxmax()
                            else:
                                start_index = df.loc[recent_level['peak_index']:waves[index]['peak_index']].iloc[1:][peak].idxmin()
                            new_start_wave_index = find_wave_index_candle_belongs_to(start_index, waves[0:index+1])
                            if waves[new_start_wave_index]['label'] == recent_level['label']:
                                new_start_wave_index = new_start_wave_index + 1
                            init_level = {
                                'secondary_key_index': recent_level['key_level_index'],
                                'key_level_index': start_index,
                                'start_index': start_index,
                                'checked_index': waves[new_start_wave_index]['checked_index'],
                                'peak_index': waves[new_start_wave_index]['peak_index'],
                                'label': waves[new_start_wave_index]['label'],
                                'type_wave': 'wave',
                                'stack_level': 0,
                                'start_stack_level': 0,
                                'false_break_level_index': None,
                                'false_break_peak_index': None,
                                'old_peak_index': None,
                                'old_key_level_index': None,
                                'old_stack_level': recent_level['stack_level'],
                            }
                            if len(waves[new_start_wave_index:index+1]) < 2:
                                recent_level = init_level
                            else:
                                recent_level = level_from_waves(df, waves[new_start_wave_index:index+1], True, init_level)
                                recent_level['start_stack_level'] = recent_level['stack_level']
                        else:
                            recent_level = {
                                'secondary_key_index': waves[index-2]['peak_index'],
                                'key_level_index': waves[index-1]['peak_index'],
                                'start_index': waves[index-2]['peak_index'],
                                'checked_index': waves[index]['checked_index'],
                                'peak_index': waves[index]['peak_index'],
                                'label': waves[index]['label'],
                                'type_wave': 'trend',
                                'stack_level': 1,
                                'start_stack_level': 1,
                                'false_break_level_index': None,
                                'false_break_peak_index': None,
                                'old_peak_index': None,
                                'old_key_level_index': None,
                                'old_stack_level': recent_level['stack_level'],
                        }
                else:
                    if index < len(waves) - 1:
                        recent_level['false_break_level_index'] = waves[index]['peak_index']
                    recent_level['checked_index'] = waves[index]['checked_index']
            else:
                recent_level['checked_index'] = waves[index]['checked_index']
        else:
            # Compare with key level peak index
            # ratio_trend = cal_ratio_trend(df, waves[index]['peak_index'], recent_level)
            # and ratio_trend < -0.25

            price_check = df.loc[recent_level['false_break_peak_index']][opposite_peak] if recent_level['false_break_peak_index'] else df.loc[recent_level['peak_index']][opposite_peak]
            expanded_checked_index = waves[index+1]['checked_index'] if index < len(waves)-1 else waves[index]['checked_index']
            if (df.loc[waves[index]['peak_index']][opposite_peak] - price_check)*direction > 0:
                if waves[index]['confirmed'] and recent_level['peak_index'] < waves[index]['start_index'] and ((df.loc[waves[index]['start_index']]['Close'] - df.loc[recent_level['peak_index']][opposite_peak])*direction > 0 or is_confirm_break(df, expanded_checked_index, price_check, waves[index]['label'], recent_level['false_break_peak_index'] or recent_level['peak_index'], primary_key)):
                    if primary_key:
                        if recent_level['label']:
                            key_level = df.loc[recent_level['peak_index']:waves[index]['peak_index']][peak].idxmin()
                        else:
                            key_level = df.loc[recent_level['peak_index']:waves[index]['peak_index']][opposite_peak].idxmax()
                        recent_level = {
                            'secondary_key_index': recent_level['peak_index'],
                            'key_level_index': key_level,
                            'start_index': recent_level['start_index'],
                            'checked_index': waves[index]['checked_index'],
                            'peak_index': waves[index]['peak_index'],
                            'label': waves[index]['label'],
                            'type_wave': 'trend',
                            'confirmed': True,
                            'stack_level': recent_level['stack_level'] + 1,
                            'start_stack_level': recent_level['start_stack_level'],
                            'false_break_level_index': None,
                            'false_break_peak_index': None,
                            'old_peak_index': recent_level['peak_index'],
                            'old_key_level_index': recent_level['key_level_index'],
                            'old_stack_level': recent_level.get('old_stack_level'),
                        }
                    else:
                        recent_level = {
                            'secondary_key_index': waves[index-2]['peak_index'],
                            'key_level_index': waves[index-1]['peak_index'],
                            'start_index': recent_level['start_index'],
                            'checked_index': waves[index]['checked_index'],
                            'peak_index': waves[index]['peak_index'],
                            'label': waves[index]['label'],
                            'type_wave': 'trend',
                            'confirmed': True,
                            'stack_level': recent_level['stack_level'] + 1,
                            'start_stack_level': recent_level['start_stack_level'],
                            'false_break_level_index': None,
                            'false_break_peak_index': None,
                            'old_peak_index': recent_level['peak_index'],
                            'old_key_level_index': recent_level['key_level_index'],
                            'old_stack_level': recent_level.get('old_stack_level'),
                        }
                else:
                    if index < len(waves) - 1:
                        recent_level['false_break_peak_index'] = waves[index]['peak_index']
                    recent_level['checked_index'] = waves[index]['checked_index']
            else:
                recent_level['checked_index'] = waves[index]['checked_index']
        index = index + 1
    return recent_level

def opposite_level_from_key_level(df, waves, key_level, is_primary):
    number_waves = count_waves_from_peak_index(key_level['peak_index'], waves, key_level['label'], True)
    if number_waves == 0:
        return None
    if number_waves == 1:
        return level_from_waves(df, [waves[-1]], is_primary)
    return level_from_waves(df, waves[-number_waves:], is_primary)

def is_wave_testing_ob(df, wave, ob_index, label_key_level):
    if label_key_level == 1:
        return df.loc[wave['peak_index']]['Low'] <= df.loc[ob_index]['High'] and \
            is_confirm_break(df, wave['peak_index'], df.loc[ob_index]['Low'], wave['label']) == False
    else:
        return df.loc[wave['peak_index']]['High'] >= df.loc[ob_index]['Low'] and \
            is_confirm_break(df, wave['peak_index'], df.loc[ob_index]['High'], wave['label']) == False

# Ob index maybe peak correction wave or any ob in range
def range_tested_price_level(df, trend_status, waves, ob_index, label_key_level):
    result = []
    if len(trend_status) < 2:
        return result
    local_waves = copy.deepcopy(waves)
    if waves[-1]['confirmed'] is False:
        local_waves.pop()
    number_waves = count_waves_from_peak_index(trend_status[1]['peak_index'], local_waves, trend_status[1]['label'])
    while number_waves >= 2:
        if is_wave_testing_ob(df, local_waves[1-number_waves], ob_index, label_key_level):
            result.append(local_waves[1-number_waves])
        number_waves = number_waves - 2
    return result

def find_peak_ob_correction(df, waves, key_level):
    number_waves = count_waves_from_peak_index(key_level['peak_index'], waves, key_level['label'])
    if number_waves == 0:
        return None
    if key_level['label']:
        return df.loc[key_level['peak_index']:key_level['checked_index']]['Low'].idxmin()
    else:
        return df.loc[key_level['peak_index']:key_level['checked_index']]['High'].idxmax()

def find_correction_wave(df, waves, key_level):
    lowest_index = find_peak_ob_correction(df, waves, key_level)
    return find_peak_wave_candle_belongs_to(lowest_index, waves, 1 - key_level['label'])

def location_compare_level(key_level):
    # if is_in_secondary_level(df, trend_status, waves, key_level):
    #     return 'secondary'
    if key_level.get('ob_correction_index') is None:
        return 'top'
    percent_latest_price = key_level['ratio_trend']
    if percent_latest_price > 1:
        return 'below'
    if percent_latest_price <= 1 and percent_latest_price > 0.8:
        return 'prebot'
    if percent_latest_price <= 0.8 and percent_latest_price >= 0.618:
        return '68'
    if percent_latest_price <= 0.3:
        return 'top'
    return 'mid'

def is_in_secondary_level(df, trend_status, waves, key_level):
    if key_level['key_level_index'] == key_level['secondary_key_index'] or len(trend_status) < 2:
        return False
    ob_index = find_peak_ob_correction(df, waves, trend_status[0])
    if ob_index is None:
        return False
    return df.loc[ob_index]['Low'] >= df.loc[key_level['secondary_key_index']]['Low'] and \
        df.loc[ob_index]['Low'] <= df.loc[key_level['secondary_key_index']]['High'] or \
        df.loc[ob_index]['High'] >= df.loc[key_level['secondary_key_index']]['Low'] and \
        df.loc[ob_index]['High'] <= df.loc[key_level['secondary_key_index']]['High']

def update_status_trend(name, timeframe, df, waves, key_level, is_primary_key=False, max_stack=4):
    global trend_status
    number_waves = count_waves_from_peak_index(key_level['peak_index'], waves, key_level['label'], True)
    trend_status[name][timeframe].append(copy.deepcopy(key_level))
    max_stack = max_stack - 1
    if number_waves == 0 or max_stack == 0:
        return
    opposite_level = level_from_waves(df, waves[-number_waves:], is_primary_key)
    update_status_trend(name, timeframe, df, waves, opposite_level, is_primary_key, max_stack)
# init_level_from_avaiable()


def update_trend_status_with_new_updated_waves(name, timeframe, waves, key_level):
    global old_trend_status, trend_status, df
    if len(trend_status[name][timeframe]) > 0 and waves[-1]['checked_index'] <= trend_status[name][timeframe][0]['checked_index']:
        print('Already update trend status latest checked index')
        return
    old_trend_status[name][timeframe] = copy.deepcopy(trend_status[name][timeframe])
    if old_trend_status[name][timeframe][0]['key_level_index'] != key_level['key_level_index'] or\
    old_trend_status[name][timeframe][0]['peak_index'] != key_level['peak_index']:
        trend_status[name][timeframe] = [copy.deepcopy(key_level)]
        return
    trend_status[name][timeframe][0] = copy.deepcopy(key_level)
    if len(old_trend_status[name][timeframe]) == 1:
        if waves[-1]['label'] != trend_status[name][timeframe][-1]['label']:
            trend_status[name][timeframe].append(level_from_waves(df[name][timeframe], [waves[-1]]))
        return
    # Check latest level first
    peak_index_wave = find_peak_index_wave_candle_belongs_to(trend_status[name][timeframe][-1]['peak_index'], waves, trend_status[name][timeframe][-1]['label'])
    new_level = level_from_waves(df[name][timeframe], waves[peak_index_wave:], True, copy.deepcopy(trend_status[name][timeframe][-1]))
    if new_level['key_level_index'] == trend_status[name][timeframe][-1]['key_level_index'] and \
    new_level['peak_index'] == trend_status[name][timeframe][-1]['peak_index']:
        trend_status[name][timeframe][-1] = new_level
        if waves[-1]['label'] != trend_status[name][timeframe][-1]['label']:
            trend_status[name][timeframe].append(level_from_waves(df[name][timeframe], [waves[-1]]))
        return
    # Check reverse order level
    trend_status[name][timeframe][-1] = new_level
    check_index = -2
    while trend_status[name][timeframe][check_index] != trend_status[name][timeframe][0]:
        peak_wave = level_to_waves(trend_status[name][timeframe][check_index])[-1]
        new_level = level_from_waves(df[name][timeframe], [peak_wave] + level_to_waves(trend_status[name][timeframe][check_index+1]), True, copy.deepcopy(trend_status[name][timeframe][check_index]))
        if new_level['key_level_index'] == trend_status[name][timeframe][check_index]['key_level_index'] and \
        new_level['peak_index'] == trend_status[name][timeframe][check_index]['peak_index']:
            trend_status[name][timeframe][check_index] = new_level
            return
        trend_status[name][timeframe][check_index] = new_level
        trend_status[name][timeframe].pop()

def detect_range_and_save_range(name, timeframe):
    global old_trend_status, trend_status, past_ranges, df
    old = old_trend_status[name][timeframe]
    new = trend_status[name][timeframe]
    ranges = past_ranges[name][timeframe]
    local_df = df[name][timeframe]
    # Check if primary level broken or continue trend:
    if old[0]['key_level_index'] != new[0]['key_level_index']:
        past_ranges[name][timeframe] = []
        print('Ranges reset')
        return
    # At least double top and double bottom to form a range
    if len(old) < 5:
        return
    # Condition broken range
    if len(old) - len(new) >= 3:
        broken_trend = find_broken_trend(old, new)
        range = {
            'start_index': broken_trend['key_level_index'],
            'end_index': new[0]['checked_index'],
            'label': new[-1]['label'],
        }
        if range['label']:
            range['valley_index'] = local_df.loc[range['start_index']:range['end_index'], 'Low'].idxmin()
        else:
            range['valley_index'] = local_df.loc[range['start_index']:range['end_index'], 'High'].idxmax()
        print('New range added')
        ranges.append(range)

def update_level_and_trend_status_with_new_row(name, timeframe, row):
    if row is None:
        return
    print(f"--Update level and trend status for {name} and timeframe: {timeframe}m")
    global df, waves, recent_level, primary_level, trend_status, old_trend_status, old_label, old_stack_level, sub_primary_level, history
    previous_checked_index = waves[name][timeframe][-1]['checked_index']
    for _, local_row in df[name][timeframe].loc[previous_checked_index:row.name].iloc[1:].iterrows():
        print('--update waves--')
        update_waves_with_new_row_df(df[name][timeframe], waves[name][timeframe], local_row)
        print('--update primary level--')
        old_label[name][timeframe] = copy.deepcopy(primary_level[name][timeframe]['label'])
        old_stack_level[name][timeframe] = copy.deepcopy(primary_level[name][timeframe]['stack_level'])
        start_index = find_peak_index_wave_candle_belongs_to(primary_level[name][timeframe]['peak_index'], waves[name][timeframe], primary_level[name][timeframe]['label'])
        updated_primary_level = level_from_waves(df[name][timeframe], waves[name][timeframe][start_index:], True, primary_level[name][timeframe])

        wave_index_start_from_key_primary = find_peak_index_wave_candle_belongs_to(updated_primary_level['key_level_index'], waves[name][timeframe], 1 - primary_level[name][timeframe]['label']) or -1
        wave_index_start_from_key_primary = wave_index_start_from_key_primary + 1

        if updated_primary_level['label'] != primary_level[name][timeframe]['label'] or \
        updated_primary_level['stack_level'] != primary_level[name][timeframe]['stack_level']:
            history['primary_level'].append(primary_level[name][timeframe])
            # Empty all sub recent level
            history['recent_level'] = []
            history['sub_primary_level'] = []

            updated_recent_level = level_from_waves(df[name][timeframe], waves[name][timeframe][wave_index_start_from_key_primary:], False)
            updated_sub_primary_level = level_from_waves(df[name][timeframe], waves[name][timeframe][wave_index_start_from_key_primary:], True)
        else:
            updated_recent_level = level_from_waves(df[name][timeframe], waves[name][timeframe][wave_index_start_from_key_primary:], False, recent_level[name][timeframe])
            updated_sub_primary_level = level_from_waves(df[name][timeframe], waves[name][timeframe][wave_index_start_from_key_primary:], True, sub_primary_level[name][timeframe])


        if len(history['recent_level']) == 0 or updated_recent_level['label'] != recent_level[name][timeframe]['label'] or \
        updated_recent_level['stack_level'] != recent_level[name][timeframe]['stack_level']:
            history['recent_level'].append(updated_recent_level)

        if len(history['sub_primary_level']) == 0 or updated_sub_primary_level['label'] != sub_primary_level[name][timeframe]['label'] or \
        updated_sub_primary_level['stack_level'] != sub_primary_level[name][timeframe]['stack_level']:
            history['sub_primary_level'].append(updated_sub_primary_level)

        primary_level[name][timeframe] = updated_primary_level
        recent_level[name][timeframe] = updated_recent_level
        sub_primary_level[name][timeframe] = updated_sub_primary_level


        # if skip_trend_status == False:
        #     print('--update trend status--')
        #     update_trend_status(df[name][timeframe], waves[name][timeframe], primary_level[name][timeframe], name, timeframe)
        #     print('--end update trend status--')

            # if len(trend_status[name][timeframe]) == 1:
            #     recent_level[name][timeframe] = copy.deepcopy(primary_level[name][timeframe])
            # else:
            #     if (len(old_trend_status[name][timeframe]) == 1 or\
            #     old_trend_status[name][timeframe][1]['key_level_index'] != trend_status[name][timeframe][1]['key_level_index'] or\
            #     old_trend_status[name][timeframe][1]['peak_index'] != trend_status[name][timeframe][1]['peak_index']):
            #         print('--update recent level correction--')
            #         recent_level[name][timeframe] = update_recent_level_from_primary(df[name][timeframe], trend_status[name][timeframe][1], waves[name][timeframe])
        # print('--Detect and save ranges---')
        # detect_range_and_save_range(name, timeframe)

def get_tail_candle(candle):
    if candle['label'] == 1:
        return [candle['Open'], candle['Low']]
    else:
        return [candle['Close'], candle['Low']]

def get_hair_candle(candle):
    if candle['label'] == 1:
        return [candle['High'], candle['Close']]
    else:
        return [candle['High'], candle['Open']]

def get_body_candle(candle):
    if candle['label'] == 1:
        return [candle['Close'], candle['Open']]
    else:
        return [candle['Open'], candle['Close']]

def is_nosd_candles_pair(first_candle, second_candle, label):
    if first_candle['label'] == label or second_candle['label'] != label:
        return False
    if label == 1:
        return second_candle['Low'] < first_candle['Low']
    else:
        return second_candle['High'] > first_candle['High']

def is_outsidebar_candles_pair(first_candle, second_candle, label):
    if is_nosd_candles_pair(first_candle, second_candle, label) == False:
        return False
    if label == 1:
        return second_candle['Close'] >= first_candle['High']
    else:
        return second_candle['Close'] <= first_candle['Low']

def is_eg_pair(first_candle, second_candle, label):
    if first_candle['label'] == label or second_candle['label'] != label:
        return False
    if label == 1:
        return is_eg_candle(second_candle) and second_candle['Close'] >= first_candle['High']
    else:
        return is_eg_candle(second_candle) and second_candle['Close'] <= first_candle['Low']

def is_meramera_candles_pair(first_candle, second_candle, label):
    if is_outsidebar_candles_pair(first_candle, second_candle, label) == False:
        return False
    body_first_candle = get_body_candle(first_candle)
    if label == 1:
        tail_second_candle = get_tail_candle(second_candle)
        return body_first_candle[0] <= tail_second_candle[0] and body_first_candle[1] >= tail_second_candle[1]
    else:
        hair_second_candle = get_hair_candle(second_candle)
        return body_first_candle[0] <= hair_second_candle[0] and body_first_candle[1] >= hair_second_candle[1]

def pattern_reg(df, wave, label, type_candle_pair_func):
    start_index = df.index.get_loc(wave['start_index'])
    checked_index = df.index.get_loc(wave['checked_index'])
    index_list = []
    for index in range(start_index, checked_index):
        first_candle = df.iloc[index-1]
        second_candle = df.iloc[index]
        if first_candle['label'] == label or second_candle['label'] != label:
            continue
        if type_candle_pair_func(first_candle, second_candle, label):
            index_list.append(first_candle.name)
    return index_list

def is_two_candle_spliting(df, first_index, second_index):
    return df.loc[first_index]['Low'] > df.loc[second_index]['High'] or df.loc[second_index]['Low'] > df.loc[first_index]['High']

def is_two_candle_touching(df, first_index, second_index):
    return is_two_candle_spliting(df, first_index, second_index) == False

def find_closest_ob_above_peak_wave(df, waves, key_level):
    wave_label = key_level['label']
    # Find break candle that is not lie on current wave
    if wave_label:
        price_top = df.loc[key_level['peak_index']]['Close'] + df.loc[key_level['start_index']]['ma_candle'] * ratio_ma_sl
    else:
        price_top = df.loc[key_level['peak_index']]['Close'] - df.loc[key_level['start_index']]['ma_candle'] * ratio_ma_sl
    break_candle_index = find_break_candle(df, key_level['start_index'], price_top, 1 - wave_label)
    if break_candle_index:
        wave_break_candle = find_peak_wave_candle_belongs_to(break_candle_index, waves, 1 - wave_label)
        previous_candle = break_candle_index
        while df.loc[previous_candle]['label'] == 1 - wave_label or is_two_candle_touching(df, key_level['peak_index'], previous_candle):
            if wave_break_candle is None or df.loc[previous_candle].name <= wave_break_candle['start_index']:
                return None
            previous_candle = df.iloc[df.index.get_loc(previous_candle) - 1].name
        return previous_candle
    return None

def find_wave_index_candle_belongs_to(candle_index, waves):
    check_index = -1
    while True:
        if waves[check_index]['start_index'] <= candle_index and waves[check_index]['checked_index'] >= candle_index:
            return check_index
        check_index = check_index - 1
        if check_index*-1 > len(waves):
            return None
        if waves[check_index]['start_index'] < candle_index and waves[check_index]['checked_index'] < candle_index:
            return check_index + 1

def find_peak_wave_candle_belongs_to(candle_index, waves, label_wave):
    check_index = -1
    while True:
        if waves[check_index]['start_index'] <= candle_index and waves[check_index]['peak_index'] >= candle_index and waves[check_index]['label'] == label_wave:
            return waves[check_index]
        check_index = check_index - 1
        if check_index*-1 > len(waves):
            return None
        if waves[check_index]['start_index'] <= candle_index and waves[check_index]['peak_index'] <= candle_index and waves[check_index]['label'] == 1 - label_wave:
            return waves[check_index + 1]

def find_peak_index_wave_candle_belongs_to(candle_index, waves, label_wave):
    check_index = -1
    while True:
        if waves[check_index]['start_index'] <= candle_index and waves[check_index]['peak_index'] >= candle_index and waves[check_index]['label'] == label_wave:
            return check_index
        check_index = check_index - 1
        if check_index*-1 > len(waves):
            return None
        if waves[check_index]['start_index'] <= candle_index and waves[check_index]['peak_index'] <= candle_index and waves[check_index]['label'] == 1 - label_wave:
            return check_index + 1

def lowest_index_from_to(df, waves, start_index, end_index, start_label, end_label):
    start_wave_index = find_peak_index_wave_candle_belongs_to(start_index, waves, start_label)
    end_wave_index = find_peak_index_wave_candle_belongs_to(end_index, waves, end_label)
    count = end_wave_index - start_wave_index
    print(f"Count waves from old key: {count}")
    if count == 0:
        return start_index
    if count == 1:
        return waves[start_wave_index+1]['peak_index']
    if start_label:
        return df.loc[waves[start_wave_index+1]['start_index']:waves[end_wave_index]['start_index'], 'Low'].idxmin()
    else:
        return df.loc[waves[start_wave_index+1]['start_index']:waves[end_wave_index]['start_index'], 'High'].idxmax()

def find_closest_ob_below_key_level(df, waves, key_level):
    if key_level['secondary_key_index'] == key_level['key_level_index']:
        return None
    last_wave = find_peak_wave_candle_belongs_to(key_level['key_level_index'], waves, 1 - key_level['label'])
    if last_wave:
        return find_closest_ob_above_peak_wave(df, waves, last_wave)
    return None


def is_unconfirm_range_break(old_trend_status, trend_status):
    return old_trend_status and old_trend_status[-1]['confirmed'] == False and trend_status[-1]['confirmed'] == True and trend_status[-1]['label'] == trend_status[0]['label']

def cal_ratio_trend(df, check_index, key_level):
    if key_level['peak_index'] == key_level['key_level_index']:
        return -1
    if key_level['label']:
        top = 'High'
        bot = 'Low'
    else:
        top = 'Low'
        bot = 'High'
    return (df.loc[key_level['peak_index']][top] - df.loc[check_index][bot])/(df.loc[key_level['peak_index']][top] - df.loc[key_level['key_level_index']][bot])

def ratio_trend_to_price(df, ratio, key_level):
    if df.loc[key_level['peak_index']]['Close'] == df.loc[key_level['key_level_index']]['Close']:
        raise ValueError('Invalid input to cal price')
    if key_level['label']:
        direction = 1
    else:
        direction = -1
    return df.loc[key_level['key_level_index'], 'Close'] + direction*abs(df.loc[key_level['peak_index']]['Close'] - df.loc[key_level['key_level_index']]['Close'])*(1-ratio)

def last_trend_with_different_label(trends):
    for trend in reversed(trends):
        if trend['label'] != trends[0]['label']:
            return trend
    return None

def is_trend_get_liquidity(df, trend, waves):
    if trend['old_key_level_index'] == None or trend['old_peak_index'] == None:
        return False
    if trend['label']:
        low = 'Low'
        direction = 1
    else:
        low = 'High'
        direction = -1
    low_index = lowest_index_from_to(df, waves, trend['old_key_level_index'], trend['old_peak_index'], trend['label'], trend['label'])
    return (df.loc[trend['key_level_index'], low] - df.loc[low_index, low])*direction < df.loc[low_index, 'ma_candle'] * liquidity_ratio

def correction_broken(df, high_df, waves, old_trend_status, trend_status, high_trend_status, recent_level):
    if trend_status[0]['label'] != old_trend_status[0]['label']:
        return False
    if cal_ratio_trend(df, waves[-1]['checked_index'], trend_status[0]) >= 0.3:
        if len(trend_status) == 1 or len(old_trend_status) == 1:
            return False
        if trend_status[1]['label'] != trend_status[0]['label'] or trend_status[1]['label'] == old_trend_status[1]['label']:
            return False
    else:
        if len(trend_status) != 1 or trend_status[0]['stack_level'] <= old_trend_status[0]['stack_level']:
            return False
    if len(high_trend_status) > 1 and high_trend_status[1]['type_wave'] == 'trend':
        if trend_status[0]['label'] != high_trend_status[1]['label']:
            return False
    else:
        if trend_status[0]['label'] != high_trend_status[0]['label']:
            return False
    # Filter liquidity
    return is_trend_get_liquidity(df, trend_status[0], waves)

def is_candle_belong_peak_level(key_level, candle_index):
    pass

def zanshin1(df, high_df, high_waves, old_trend_status, trend_status, high_trend_status, recent_level):
    global waiting_stack_level_confirm, ratio_before, ratio_after
    if waiting_stack_level_confirm and old_trend_status[0]['label'] == trend_status[0]['label'] and trend_status[0]['stack_level'] > old_trend_status[0]['stack_level'] and trend_status[0]['stack_level'] >= 1:
        waiting_stack_level_confirm = False
        return True, None
    if old_trend_status[0]['label'] == trend_status[0]['label']:
        return False, None
    if trend_status[0]['label'] != high_trend_status[0]['label']:
        waiting_stack_level_confirm = False
        return False, None
    if count_waves_from_peak_index(high_trend_status[0]['peak_index'], high_waves, high_trend_status[0]['label']) > 0 and cal_ratio_trend(high_df, high_trend_status[0]['checked_index'], high_trend_status[0]) < 0.3:
        return False, None
    waiting_stack_level_confirm = True
    return False, None

def is_wave_have_range(check_index, waves, label):
    wave = find_peak_wave_candle_belongs_to(check_index, waves, label)
    return len(wave['ranges']) > 1 or wave['ranges'][0]['count'] > 11

def is_double_range_break(break_candle, waves, label):
    wave_index = find_wave_index_candle_belongs_to(break_candle, waves)
    count = 0
    while wave_index <= -1:
        if waves[wave_index]['label'] == label:
            for range in waves[wave_index]['ranges']:
                if range['count'] >= 8:
                    count = count + 1
                if range['count'] >= 30:
                    return True
        else:
            count = 0
        if count == 2:
            return True
        wave_index = wave_index + 1
    return False

def is_price_on_edge(df_key, key, df_check_index, check_index, label_wave):
    if label_wave == key['label']:
        price = df_check_index.loc[check_index]['High']
        return price >= df_key.loc[key['peak_index'], 'Low']
    else:
        price = df_check_index.loc[check_index]['Low']
        return price <= df_key.loc[key['key_level_index'], 'High']


def zanshin2(df, high_df, waves, old_trend_status, trend_status, high_trend_status, high_key):
    global broken_trendline, is_trendline_broken
    if is_trendline_broken:
        if len(trend_status) <= 1 or len(old_trend_status) <= 1:
            is_trendline_broken = False
            return False, None
        if trend_status[1]['label'] == old_trend_status[1]['label'] and trend_status[1]['stack_level'] > old_trend_status[1]['stack_level']:
                is_trendline_broken = False
                return False, None
        else:
            if is_price_on_edge(high_df, high_key, df, trend_status[0]['checked_index'], trend_status[0]['label']):
                is_trendline_broken = False
                return False, None
            if is_double_range_break(broken_trendline['break_candle'], waves, trend_status[0]['label']) or \
                trend_status[-1]['label'] == trend_status[0]['label'] and trend_status[-1]['stack_level'] >= 2:
                is_trendline_broken = False
                return True, None
            return False, None
    if len(trend_status) > 1 and trend_status[1]['label'] != trend_status[0]['label'] and is_break_trend_line(df, waves, trend_status[1]):
        is_trendline_broken = True
        broken_trendline = {
            'start_index': trend_status[1]['old_key_level_index'],
            'end_index': trend_status[1]['key_level_index'],
            'label': trend_status[1]['label'],
            'break_candle': trend_status[1]['checked_index']
        }
    return False, None


def is_higher_peak_correction(df, trend_status, label_correction):
    last_trend = trend_status[-1]
    if last_trend['label'] != label_correction:
        return False
    if label_correction == 0:
        peak = 'Low'
        direction = 1
    else:
        peak = 'High'
        direction = -1
    peak_price = df.loc[last_trend['peak_index'], peak]
    for trend in reversed(trend_status):
        if trend['label'] != label_correction:
            check_index = trend['start_index']
        else:
            check_index = trend['peak_index']
        if (df.loc[check_index][peak] - peak_price)*direction < 0:
            return False
    return True

def range_fake_breakout(df, waves, old_trend_status, trend_status, high_trend_status, low_trend_status):
    global break_index
    if is_wave_in_range(high_trend_status):
        return False
    if trend_status[0]['label'] != old_trend_status[0]['label']:
        return False
    if len(trend_status) == 1 or len(old_trend_status) < 4:
        return False
    # case 1: real break range but not break key level
    if trend_status[1]['label'] != trend_status[0]['label'] and len(trend_status) == 2:
        break_index = trend_status[1]['secondary_key_index']
        print('fake_breakout_type1')
        return True
    # case 2: fake range breakout
    if is_higher_peak_correction(df, trend_status, 1 - trend_status[0]['label']):
        print('fake_breakout_type2')
        return True
    return False


def range_fake_breakout_confirm(df, waves, old_trend_status, trend_status):
    global fake_breakout_checking, break_index
    if trend_status[0]['label'] != old_trend_status[0]['label'] or len(trend_status) == 1:
        # primary trend change
        fake_breakout_checking = False
        break_index = None
        return False
    if len(trend_status) == 2 and trend_status[1]['label'] == trend_status[0]['label']:
        fake_breakout_checking = False
        break_index = None
        return False
    if trend_status[0]['label'] == 1:
        high = 'High'
        direction = 1
    else:
        high = 'Low'
        direction = -1
    if (df.loc[waves[-1]['peak_index']]['Close'] - df.loc[break_index][high])*direction >= 0:
        return True
    return False

def main_trend_continue(df, waves, old_trend_status, trend_status, high_trend_status):
    if trend_status[0]['label'] != old_trend_status[0]['label']:
        return False
    if trend_status[0]['stack_level'] > 0 and is_trend_has_imbalance(df, trend_status[0]):
        return True
    return False

def is_wave_range_break(old_trend_status, trend_status):
    if is_wave_in_range(old_trend_status) == False:
        return False
    return len(trend_status) <= 2 and trend_status[-1]['label'] == trend_status[0]['label']

def is_wave_in_range(trend_status):
    return len(trend_status) >= 3

def is_weak_trend(df, trend_status):
    if trend_status['secondary_key_index'] != trend_status['key_level_index']:
        ratio = cal_ratio_trend(df, trend_status['secondary_key_index'], trend_status)
        return ratio < weak_trend_ratio
    else:
        return False

def is_break_trend_line(df, waves, recent_level):
    # Previous low not working need fix
    previous_low = recent_level['old_key_level_index']
    if previous_low == None:
        return False
    # Find func trend line:
    if recent_level['label'] == 1:
        direction = 1
    else:
        direction = -1

    y2 = df.loc[recent_level['key_level_index'], 'Close']
    y1 = df.loc[previous_low, 'Close']
    x2 = df.index.get_loc(recent_level['key_level_index'])
    x1 = df.index.get_loc(previous_low)
    m1 = (y2 - y1) / (x2 - x1)
    c1 = y1 - m1 * x1
    x_intersect = df.index.get_loc(waves[-1]['checked_index'])
    y_intersect = m1 * x_intersect + c1

    return (df.loc[waves[-1]['checked_index'], 'Close'] - y_intersect)*direction < 0


def is_wave_has_valid_range(waves):
    count_each = 0
    for wave in waves:
        if len(wave['ranges']) >=2:
            count = 0
            for range in wave['ranges']:
                count = count + range['count']
            if count >= 20:
                return True
        else:
            if wave['confirmed']:
                count_each = count_each + wave['ranges'][0]['count']
            else:
                count_each = count_each + len(wave)
            if count_each >= 20:
                return True
    return False


def find_previous_lowest_index_same_label(df, waves, level, is_primary=False):
    number_waves = count_waves_from_peak_index(level['peak_index'], waves, level['label']) + 1
    if len(waves) == number_waves:
        return None
    previous_level = level_from_waves(df, waves[:-number_waves], is_primary)
    if previous_level['label'] != level['label']:
        # Using peak previous level different label as lowest point
        return previous_level['peak_index']
    return previous_level['key_level_index']

def is_testing_key_level(df, key_level, check_index):
    if key_level['label']:
        return df.loc[key_level['key_level_index']]['High'] >= df.loc[check_index]['Low']
    else:
        return df.loc[key_level['key_level_index']]['Low'] <= df.loc[check_index]['High']

def break_range_reverse(df, waves, key_level, trend_status, old_trend_status):
    if old_trend_status[0]['label'] != trend_status[0]['label']:
        return False, None
    if len(trend_status) == 1 or is_testing_key_level(df, key_level, waves[-1]['checked_index']) == False:
        return False, None
    if len(trend_status) == 2 and trend_status[1]['stack_level'] >= 1:
        if is_wave_has_valid_range(waves[-2:]):
            return True, None
    if len(trend_status) >= 3:
        return True, None
    return False, None

def bos_detect(df, waves, key_level, trend_status, old_trend_status):
    global bos_signal, lowest_point, bos_checked_index, bos_index
    if key_level['label']:
        direction = 1
        low = 'Low'
        high = 'High'
    else:
        direction = -1
        low = 'High'
        high = 'Low'
    if bos_signal:
        if (df.loc[waves[-1]['checked_index'], 'Close'] - df.loc[lowest_point][low])*direction < 0 or\
        key_level['label'] != old_trend_status[0]['label'] or\
        key_level['stack_level'] != old_trend_status[0]['stack_level']:
            bos_signal = False
            return False, None
        if (df.loc[waves[-1]['checked_index']]['Close'] - df.loc[bos_index]['Close'])*direction > 0:
            if key_level['label']:
                bos_checked_index = df.loc[bos_index:waves[-1]['checked_index']][low].idxmin()
            else:
                bos_checked_index = df.loc[bos_index:waves[-1]['checked_index']][low].idxmax()
            if (df.loc[bos_index][high]-df.loc[bos_checked_index][low])/(df.loc[bos_index][high]-df.loc[lowest_point][low]) < 0.5:
                bos_index = waves[-1]['checked_index']
                return False, None
            else:
                # Check effort
                if df.loc[lowest_point:bos_index]['Volume'].sum()/abs(df.loc[lowest_point]['Open'] - df.loc[bos_index]['Close']) >\
                df.loc[bos_checked_index:waves[-1]['checked_index']]['Volume'].sum()/abs(df.loc[bos_checked_index]['Open'] - df.loc[waves[-1]['checked_index']]['Close']):
                    bos_signal = False
                    return True, None
                else:
                    bos_index = waves[-1]['checked_index']
                    return False, None

        # checked_candles = df.loc[bos_checked_index:waves[-1]['checked_index']]
        # if len(checked_candles) >= 2 and (checked_candles.iloc[1]['Close'] - checked_candles.iloc[0]['Close'])*direction > 0 and\
        # (checked_candles.iloc[1]['Close'] - checked_candles.iloc[0]['Open'])*direction > 0 and \
        # (checked_candles.iloc[1]['effort'] - checked_candles.iloc[0]['effort'])*direction < 0 and is_eg_candle(checked_candles.iloc[1]):
        #     bos_signal = False
        #     return True, None
        # else:
        #     bos_checked_index = df.loc[bos_checked_index:].iloc[1].name if len(checked_candles) >= 2 else bos_checked_index
        #     return False, None
    if bos_signal == False and len(trend_status) > 1 and trend_status[1]['label'] != key_level['label'] and (df.loc[waves[-1]['checked_index'], 'Close'] - df.loc[trend_status[1]['key_level_index']]['Close'])*direction > 0 and\
    cal_ratio_trend(df, trend_status[1]['peak_index'], trend_status[0]) >= 0.5:
        bos_signal = True
        lowest_point = copy.deepcopy(trend_status[1]['peak_index'])
        bos_index = copy.deepcopy(waves[-1]['checked_index'])
        bos_checked_index = bos_index
    return False, None

def smc(name, timeframe, high_tf, very_high_tf):
    global df, primary_level, old_trend_status, trend_status
    if old_trend_status[name][timeframe][0]['label'] != trend_status[name][timeframe][0]['label'] or old_trend_status[name][timeframe][0]['stack_level'] != trend_status[name][timeframe][0]['stack_level']:
        # Key level change peak or key, find smc ob
        primary_level[name][timeframe]['smc_ob_list'] = find_smc_ob(df[name][timeframe], primary_level[name][timeframe])
    if primary_level[name][timeframe].get('smc_ob_list') is None:
        return False, None
    if primary_level[name][high_tf]['label'] != primary_level[name][timeframe]['label'] or primary_level[name][timeframe]['label'] != primary_level[name][very_high_tf]['label']:
        return False, None
    if primary_level[name][timeframe]['label']:
        high = 'High'
        low = 'Low'
        direction = 1
    else:
        high = 'Low'
        low = 'High'
        direction = -1
    for ob in primary_level[name][timeframe].get('smc_ob_list'):
       if (df[name][timeframe].loc[ob['ob_index']][high] - df[name][timeframe].loc[waves[name][timeframe][-1]['checked_index']][low])*direction > 0:
           return True, None
    return False, None

def confluent_key(name, timeframe, high_tf, very_high_tf):
    global df, primary_level, old_trend_status, trend_status
    if primary_level[name][high_tf]['label'] != primary_level[name][very_high_tf]['label'] or primary_level[name][timeframe]['label'] != primary_level[name][high_tf]['label']:
        return False, None
    #  Is test discount zone
    if primary_level[name][high_tf].get('ratio_trend') and (primary_level[name][high_tf]['ratio_trend'] < 0.618 or primary_level[name][very_high_tf]['ratio_trend'] < 0.618):
        return False, None
    if primary_level[name][timeframe]['label']:
        high = 'High'
        low = 'Low'
        direction = 1
    else:
        high = 'Low'
        low = 'High'
        direction = -1
    if (df[name][timeframe].loc[primary_level[name][timeframe]['key_level_index']][high] - df[name][timeframe].loc[waves[name][timeframe][-1]['checked_index']][low])*direction > 0:
        entry = df[name][timeframe].loc[waves[name][timeframe][-1]['checked_index']]['Close']
        if primary_level[name][high_tf]['label'] == primary_level[name][timeframe]['label']:
            ob_sl, tf_sl = primary_level[name][timeframe]['key_level_index'], timeframe
        else:
            ob_sl, tf_sl = primary_level[name][timeframe]['peak_index'], timeframe
        ob_tp = primary_level[name][very_high_tf]['peak_index']
        return True, (entry, ob_sl, tf_sl , ob_tp, very_high_tf, primary_level[name][high_tf]['label'],)
    return False, None

def trend_reverse(name, timeframe, high_tf, very_high_tf):
    global old_label, old_stack_level, primary_level
    if old_label[name][timeframe] != primary_level[name][timeframe]['label']:
        return True, None
    return False, None


def is_coc(name, main_tf, high_tf, very_high_tf):
    global old_trend_status, trend_status, df, waves, fake_breakout_checking, primary_level, recent_level

    # if correction_broken(df[name][main_tf], df[name][high_tf], waves[name][main_tf], old_trend_status[name][main_tf], trend_status[name][main_tf], trend_status[name][high_tf], recent_level[name][timeframe]):
    #     return 'correction_broken'

    # if location in coc_pattern_point['main_trend_continue']['location'] and main_trend_continue(df[name][main_tf], waves[name][main_tf], old_trend_status[name][main_tf], trend_status[name][main_tf], trend_status[name][high_tf]):
    #     return 'main_trend_continue'

    # result_zanshin1, sl_tp = zanshin1(df[name][main_tf], df[name][high_tf], waves[name][high_tf], old_trend_status[name][main_tf], trend_status[name][main_tf], trend_status[name][high_tf], recent_level[name][timeframe])
    # if result_zanshin1:
    #     return 'zanshin1', sl_tp

    # result_zanshin2, entry_sl_tp = zanshin2(df[name][main_tf], df[name][high_tf], waves[name][main_tf], old_trend_status[name][main_tf], trend_status[name][main_tf], trend_status[name][high_tf], primary_level[name][high_tf])
    # if result_zanshin2:
    #     return 'zanshin2', entry_sl_tp

    # result_bos, entry_sl_tp = bos_detect(df[name][main_tf], waves[name][main_tf], primary_level[name][main_tf], trend_status[name][main_tf], old_trend_status[name][timeframe])
    # if result_bos:
    #     return 'bos', entry_sl_tp

    # result_brr, entry_sl_tp = break_range_reverse(df[name][main_tf], waves[name][main_tf], primary_level[name][main_tf], trend_status[name][main_tf], old_trend_status[name][timeframe])
    # if result_brr:
    #     return 'break_range_reverse', entry_sl_tp

    # result_confluent_key, entry_sl_tp = confluent_key(name, main_tf, high_tf, very_high_tf)
    # if result_confluent_key:
    #     return 'confluent_key', entry_sl_tp

    result_trend_reverse, entry_sl_tp = trend_reverse(name, main_tf, high_tf, very_high_tf)
    if result_trend_reverse:
        return 'trend_reverse', entry_sl_tp

    # result_smc, entry_sl_tp = smc(name, main_tf, high_tf, very_high_tf)
    # if result_smc:
    #     return 'smc', entry_sl_tp


    # result, entry_sl_tp = range_fake_breakout(df[name][main_tf], waves[name][main_tf], old_trend_status[name][main_tf], trend_status[name][main_tf], trend_status[name][high_tf]):
    # if result:
    #     return 'range_fake_breakout', entry_sl_tp


    # if is_wave_range_break(old_trend_status[name][main_tf], trend_status[name][main_tf]):
    #     return 'is_wave_range_break'
    # if is_low_tf_opposite_recent_reverse(old_trend_status[name][low_tf], trend_status[name][low_tf]):
    #     return 'is_low_tf_opposite_recent_reverse'
    # if is_unconfirm_range_break(old_trend_status[name][main_tf], trend_status[name][main_tf]):
    #     return 'is_unconfirm_range_break'
    # opposite_recent_level = opposite_level_from_key_level(df[name][main_tf], waves[name][main_tf], primary_level[name][main_tf], False)
    # if opposite_recent_level != None and is_break_trend_line(df[name][main_tf], waves[name][main_tf], opposite_recent_level):
    #     return 'is_break_trend_line'
    return None, None

def candle_pattern(df, wave, label):
    for name, candle in candles_pattern_point.items():
        if len(pattern_reg(df, wave, label, candle['reg'])) >= candle['count']:
            return name
    return None

def total_point_from_signal_code(signal_code):
    total = location_point[signal_code[0]] + coc_pattern_point[signal_code[1]]['point']
    if signal_code[2]:
        total = total + candles_pattern_point[signal_code[2]]['point']
    return total

def update_trend_status(df, waves, key_level, name, timeframe):
    global old_trend_status, trend_status
    if len(trend_status[name][timeframe]) > 0 and waves[-1]['checked_index'] <= trend_status[name][timeframe][0]['checked_index']:
        print('Already update trend status latest checked index')
        return
    old_trend_status[name][timeframe] = copy.deepcopy(trend_status[name][timeframe])
    trend_status[name][timeframe] = []
    update_status_trend(name, timeframe, df, waves, key_level, False)

def opposite_ob(wave, df):
    start_date = wave['start_index']
    label = wave['label']
    result = []

    # Get all dates after start_date (exclusive)
    dates = df.index[df.index > start_date]

    for date in dates:
        pos = df.index.get_loc(date)
        # Check candle type based on wave label
        if label == 1:
            # Up wave: look for red candles (close < open)
            if df.loc[date, 'Close'] >= df.loc[date, 'Open']:
                continue
            # Get adjacent candles
            prev_pos = pos - 1
            next_pos = pos + 1
            prev_date = df.index[prev_pos] if prev_pos >= 0 else None
            next_date = df.index[next_pos] if next_pos < len(df.index) else None
            # Collect low prices
            lows = []
            if prev_date is not None:
                lows.append(df.loc[prev_date, 'Low'])
            lows.append(df.loc[date, 'Low'])
            if next_date is not None:
                lows.append(df.loc[next_date, 'Low'])
            # Check if current date's low is the minimum
            if df.loc[date, 'Low'] == min(lows):
                result.append(date)
        else:
            # Down wave: look for green candles (close > open)
            if df.loc[date, 'Close'] <= df.loc[date, 'Open']:
                continue
            # Get adjacent candles
            prev_pos = pos - 1
            next_pos = pos + 1
            prev_date = df.index[prev_pos] if prev_pos >= 0 else None
            next_date = df.index[next_pos] if next_pos < len(df.index) else None
            # Collect high prices
            highs = []
            if prev_date is not None:
                highs.append(df.loc[prev_date, 'High'])
            highs.append(df.loc[date, 'High'])
            if next_date is not None:
                highs.append(df.loc[next_date, 'High'])
            # Check if current date's high is the maximum
            if df.loc[date, 'High'] == max(highs):
                result.append(date)
    return result

def get_trendline(wave, df):
    start_date = wave['start_index']
    ob_dates = opposite_ob(wave, df)
    all_dates = [start_date] + ob_dates
    all_dates_sorted = sorted(all_dates)

    # Collect relevant price points
    label = wave.label
    points = []
    for date in all_dates_sorted:
        if label == 1:
            price = df.loc[date, 'Low']
        else:
            price = df.loc[date, 'High']
        x = date.toordinal()
        points.append((x, price))

    if len(points) < 1:
        return (0.0, 0.0)  # Fallback, though all_dates includes start_date

    # If only one point, return horizontal line
    if len(points) == 1:
        return (0.0, points[0][1])

    best_slope = -np.inf if label == 1 else np.inf
    best_intercept = None

    # Check all pairs of points
    n = len(points)
    for i in range(n):
        for j in range(i + 1, n):
            x1, y1 = points[i]
            x2, y2 = points[j]
            if x2 == x1:
                continue  # Avoid division by zero
            slope = (y2 - y1) / (x2 - x1)
            intercept = y1 - slope * x1

            # Check if all points are on the correct side of the line
            valid = True
            for (xk, yk) in points:
                y_line = slope * xk + intercept
                if label == 1:
                    if yk < y_line - 1e-9:  # Allowing for floating point precision
                        valid = False
                        break
                else:
                    if yk > y_line + 1e-9:
                        valid = False
                        break
            if valid:
                if label == 1:
                    if slope > best_slope:
                        best_slope = slope
                        best_intercept = intercept
                else:
                    if slope < best_slope:
                        best_slope = slope
                        best_intercept = intercept

    # Handle case where no valid line found (use first and last point)
    if best_intercept is None:
        x1, y1 = points[0]
        x2, y2 = points[-1]
        if x2 == x1:
            best_slope = 0.0
            best_intercept = y1
        else:
            best_slope = (y2 - y1) / (x2 - x1)
            best_intercept = y1 - best_slope * x1

    return (best_slope, best_intercept)

def is_marubozu(row, direction):
    body = abs(row['Close'] - row['Open'])
    total_range = row['High'] - row['Low']
    if total_range == 0:
        return False
    body_ratio = body / total_range
    if direction == 'green' and row['Close'] > row['Open']:
        return body_ratio >= 0.9
    elif direction == 'red' and row['Close'] < row['Open']:
        return body_ratio >= 0.9
    return False

def find_obs(df):
    obs = []
    for i in range(len(df) - 2):
        current = df.iloc[i]
        next1 = df.iloc[i+1]
        next2 = df.iloc[i+2]
        # Check for bullish OB (red followed by two green marubozu)
        if current['Close'] < current['Open']:
            if is_marubozu(next1, 'green') and is_marubozu(next2, 'green'):
                obs.append({'index': i, 'type': 'bullish', 'key_level': current['Low']})
        # Check for bearish OB (green followed by two red marubozu)
        elif current['Close'] > current['Open']:
            if is_marubozu(next1, 'red') and is_marubozu(next2, 'red'):
                obs.append({'index': i, 'type': 'bearish', 'key_level': current['High']})
    return obs

def find_double_tops_bottoms(df, threshold_pct=0.005):
    peaks = []
    troughs = []
    for i in range(1, len(df)-1):
        prev = df.iloc[i-1]
        current = df.iloc[i]
        next_c = df.iloc[i+1]
        if current['High'] > prev['High'] and current['High'] > next_c['High']:
            peaks.append(i)
        if current['Low'] < prev['Low'] and current['Low'] < next_c['Low']:
            troughs.append(i)

    double_tops = []
    for i in range(len(peaks)):
        for j in range(i+1, len(peaks)):
            peak1_high = df.iloc[peaks[i]]['High']
            peak2_high = df.iloc[peaks[j]]['High']
            if abs(peak1_high - peak2_high) / peak1_high <= threshold_pct:
                trough_between = [t for t in troughs if peaks[i] < t < peaks[j]]
                if trough_between:
                    trough_low = df.iloc[trough_between[0]]['Low']
                    for k in range(peaks[j], len(df)):
                        if df.iloc[k]['Low'] < trough_low:
                            double_tops.append({'type': 'top', 'key_level': max(peak1_high, peak2_high)})
                            break
                    break

    double_bottoms = []
    for i in range(len(troughs)):
        for j in range(i+1, len(troughs)):
            trough1_low = df.iloc[troughs[i]]['Low']
            trough2_low = df.iloc[troughs[j]]['Low']
            if abs(trough1_low - trough2_low) / trough1_low <= threshold_pct:
                peak_between = [p for p in peaks if troughs[i] < p < troughs[j]]
                if peak_between:
                    peak_high = df.iloc[peak_between[0]]['High']
                    for k in range(troughs[j], len(df)):
                        if df.iloc[k]['High'] > peak_high:
                            double_bottoms.append({'type': 'bottom', 'key_level': min(trough1_low, trough2_low)})
                            break
                    break
    return double_tops + double_bottoms

def check_weep(df, key_level, is_above):
    for i in range(len(df)):
        candle = df.iloc[i]
        if is_above:
            if candle['High'] > key_level:
                for j in range(i, len(df)):
                    if df.iloc[j]['Close'] < key_level:
                        return True
                break
        else:
            if candle['Low'] < key_level:
                for j in range(i, len(df)):
                    if df.iloc[j]['Close'] > key_level:
                        return True
                break
    return False

def is_weep_liquidity(df):
    obs = find_obs(df)
    double_levels = find_double_tops_bottoms(df)

    wept_count = 0

    # Check OBs
    for ob in obs:
        start_idx = ob['index'] + 1
        key_level = ob['key_level']
        if ob['type'] == 'bullish':
            is_weep = check_weep(df.iloc[start_idx:], key_level, is_above=False)
        else:
            is_weep = check_weep(df.iloc[start_idx:], key_level, is_above=True)
        if is_weep:
            wept_count += 1

    # Check double tops/bottoms
    for level in double_levels:
        key_level = level['key_level']
        if level['type'] == 'top':
            is_weep = check_weep(df, key_level, is_above=True)
        else:
            is_weep = check_weep(df, key_level, is_above=False)
        if is_weep:
            wept_count += 1

    if (wept_count >= 2) or (wept_count >=1 and (len(obs) + len(double_levels)) >=1):
        return [True, wept_count]
    else:
        return [False, 0]

def level_to_waves(level):
    if level['secondary_key_index'] == level['key_level_index']:
        return [{
            'start_index': level['start_index'],
            'checked_index': level['checked_index'],
            'peak_index': level['peak_index'],
            'confirmed': True,
            'label': level['label'],
        }]
    else:
        return [
            {
                'start_index': level['start_index'],
                'checked_index': level['secondary_key_index'],
                'peak_index': level['secondary_key_index'],
                'confirmed': True,
                'label': level['label'],
            },
            {
                'start_index': level['secondary_key_index'],
                'checked_index': level['key_level_index'],
                'peak_index': level['key_level_index'],
                'confirmed': True,
                'label': 1 - level['label'],
            },
            {
                'start_index': level['key_level_index'],
                'checked_index': level['checked_index'],
                'peak_index': level['peak_index'],
                'confirmed': True,
                'label': level['label'],
            }
        ]

def get_combinewave_from_low_tf(start_index, checked_index, waves):
    index = 1
    end_index = 1
    if waves[0]['start_index'] > checked_index:
        return None
    while waves[-index]['start_index'] < start_index:
        index = index + 1
    while waves[-end_index]['checked_index'] > checked_index:
        end_index = end_index + 1
    end_index = 1 - end_index or None
    combine_waves = waves[index:end_index]
    return {
        'start_index': combine_waves[0]['start_index'],
        'checked_index': combine_waves[-1]['checked_index']
    }


location_point = {
    'top': 1,
    'bot': 2,
    'prebot': 1,
    'below': 3,
    'mid': 1,
    '68': 2,
    'secondary': 2,
}

candles_pattern_point = {
    'eg': { 'point': 4, 'reg': is_eg_pair, 'count': 1 },
    'mera': { 'point': 3, 'reg': is_meramera_candles_pair, 'count': 1 },
    'osb': { 'point': 2, 'reg': is_outsidebar_candles_pair, 'count': 1 },
    'nosd': { 'point': 1, 'reg': is_nosd_candles_pair, 'count': 2 },
}

coc_pattern_point = {
    'correction_broken': {
        'location': ['bot', '68', 'below', 'prebot', 'secondary', 'mid', 'top'],
        'point': 4,
        'entry_now': True,
        'reverse_mode': False,
        'require_ob_tp': False,
        'candle_check': False,
    },
    'zanshin1': {
        'location': ['bot', '68', 'below', 'prebot', 'secondary', 'mid', 'top'],
        'point': 4,
        'entry_now': True,
        'reverse_mode': False,
        'require_ob_tp': False,
        'candle_check': False,
    },
    'zanshin2': {
        'location': ['bot', '68', 'below', 'prebot', 'secondary', 'mid', 'top'],
        'point': 4,
        'entry_now': True,
        'reverse_mode': False,
        'require_ob_tp': False,
        'candle_check': False,
        'max_open_trade': 1,
    },
    'bos': {
        'location': ['bot', '68', 'below', 'prebot'],
        'point': 4,
        'entry_now': True,
        'reverse_mode': False,
        'require_ob_tp': False,
        'candle_check': False,
        'max_open_trade': 1,
    },
    'break_range_reverse': {
        'location': ['bot', 'below', 'prebot'],
        'point': 4,
        'entry_now': True,
        'reverse_mode': False,
        'require_ob_tp': False,
        'candle_check': False,
        'max_open_trade': 1,
    },
    'smc': {
        'location': ['68'],
        'point': 4,
        'entry_now': True,
        'reverse_mode': False,
        'require_ob_tp': False,
        'candle_check': False,
        'max_open_trade': 1,
    },
    'confluent_key': {
        'location': ['68', 'bot', 'prebot'],
        'point': 4,
        'entry_now': True,
        'reverse_mode': False,
        'require_ob_tp': False,
        'candle_check': False,
        'max_open_trade': 1,
    },
    'trend_reverse': {
        'location': ['68', 'bot', 'prebot'],
        'point': 4,
        'entry_now': True,
        'reverse_mode': False,
        'require_ob_tp': False,
        'candle_check': False,
        'max_open_trade': 1,
    },
    'main_trend_continue': {
        'location': ['bot', 'below', '68', 'prebot', 'secondary'],
        'point': 4,
        'entry_now': True,
        'reverse_mode': False,
        'require_ob_tp': False,
        'candle_check': True,
    },
    'range_fake_breakout': {
        'location': ['bot', '68', 'below', 'prebot'],
        'point': 4,
        'entry_now': True,
        'reverse_mode': False,
        'require_ob_tp': False,
        'candle_check': False,
    },
    'is_wave_range_break': 3,
    'is_low_tf_opposite_recent_reverse': 2,
    'is_unconfirm_range_break': 2,
    'is_break_trend_line': 1,
}

def analyze_point(name, main_tf, high_tf, very_high_tf):
    global df, waves, recent_level, primary_level, trend_status
    signal_code = []
    location = location_compare_level(primary_level[name][main_tf])
    coc_pattern, entry_sl_tp = is_coc(name, main_tf, high_tf, very_high_tf)
    # Must have coc pattern to long or short
    if coc_pattern == None:
        return (None,) * 3
    # Location code
    signal_code.append(location)
    # COC signal code
    signal_code.append(coc_pattern)
    # Prepare for pattern candle code

    if coc_pattern_point[coc_pattern]['candle_check']:
        if waves[name][main_tf][-1]['confirmed'] == False:
            combine_wave = {
                'start_index': waves[name][main_tf][-2]['start_index'],
                'checked_index': waves[name][main_tf][-1]['checked_index'],
                'peak_index': waves[name][main_tf][-2]['peak_index'],
                'confirmed': True,
                'label': waves[name][main_tf][-2]['label']
            }
        else:
            combine_wave = waves[name][main_tf][-1]
        signal_code.append(candle_pattern(df[name][main_tf], combine_wave, primary_level[name][main_tf]['label']))
    else:
        signal_code.append(None)
    return signal_code, total_point_from_signal_code(signal_code), entry_sl_tp


def set_default_tp(df, waves, key_level, direction, signal_code, sl):
    tp = df.loc[key_level['peak_index'], 'High' if key_level['label'] else 'Low']
    es_entry = df.loc[waves[-1]['checked_index'], 'Close']
    if signal_code[0] == 'top':
        relate_ob = find_break_candle(df, key_level['start_index'], tp, 1 - key_level['label'])
        if relate_ob:
            relate_wave = find_peak_wave_candle_belongs_to(relate_ob, waves, 1 - key_level['label'])
            tp = df.loc[relate_wave['start_index'], 'Low' if key_level['label'] else 'High']
            if (tp - df.loc[waves[-1]['checked_index'], 'Close'])*direction > 0 and cal_rr(es_entry, sl, tp) >= min_rr:
                return relate_wave['start_index'], tp
    while (tp - df.loc[waves[-1]['checked_index'], 'Close'])*direction <= 0 or cal_rr(es_entry, sl, tp) < min_rr:
        tp = tp + df.loc[key_level['checked_index'], 'ma_candle']*direction*ratio_ma_tp
    return None, tp

def find_tp(df, waves, key_level, signal_code, old_trend_status, sl):
    if key_level['label'] == 1:
        direction = 1
        low = 'Low'
        high = 'High'
        min_func = min
    else:
        direction = -1
        high = 'Low'
        low = 'High'
        min_func = max
    if (df.loc[waves[-1]['checked_index'], 'Close'] - df.loc[key_level['peak_index'], 'Close'])*direction > 0 or\
        abs(df.loc[waves[-1]['checked_index'], 'Close'] - df.loc[key_level['peak_index'], 'Close']) <= df.loc[key_level['checked_index']]['ma_candle'] * ratio_ma_tp:
        return set_default_tp(df, waves, key_level, direction, signal_code, sl)

    number_waves_after_peak = count_waves_from_peak_index(key_level['peak_index'], waves, key_level['label'], True)
    if number_waves_after_peak > 0:
        ob_index = None
        sub_level = opposite_level_from_key_level(df, waves, key_level, True)
        if sub_level and sub_level['label'] != key_level['label'] and df.loc[sub_level['key_level_index']][low] - df.loc[waves[-1]['peak_index'], high] > 0:
            if (df.loc[sub_level['key_level_index']][low] - df.loc[waves[-1]['checked_index'], 'Close'])*direction > df.loc[key_level['checked_index']]['ma_candle'] * ratio_ma_tp:
                ob_index = sub_level['key_level_index']
        else:
            if (df.loc[key_level['peak_index']][low] - df.loc[waves[-1]['checked_index'], 'Close'])*direction > df.loc[key_level['checked_index']]['ma_candle'] * ratio_ma_tp:
                ob_index = key_level['peak_index']
        if ob_index:
            return ob_index, min_func(df.loc[ob_index]['Open'], df.loc[ob_index]['Close']) - direction*df.loc[key_level['checked_index']]['ma_candle']*ratio_ma_tp
    return set_default_tp(df, waves, key_level, direction, signal_code, sl)

def last_trend_with_same_label(trend_status):
    label = trend_status[0]['label']
    if len(trend_status) == 1:
        return trend_status[0]
    else:
        check_index = -1
        while check_index*-1 <= len(trend_status):
            if trend_status[check_index]['label'] == label and trend_status[check_index]['confirmed']:
                return trend_status[check_index]
            check_index = check_index - 1

def find_sl(df, waves, key_level, signal_code, old_trend_status, trend_status):
    ob_index = None
    if key_level['label'] == 1:
        low = 'Low'
        high = 'High'
        direction = 1
    else:
        low = 'High'
        high = 'Low'
        direction = -1
    if signal_code[0] in ['below']:
        ob_index = find_closest_ob_below_key_level(df, waves, key_level)
    if signal_code[0] in ['top']:
        ob_index = trend_status[0]['key_level_index']
    if signal_code[0] in ['secondary', 'mid']:
        if len(trend_status) > 1:
            if trend_status[1]['label'] == trend_status[0]['label']:
                ob_index = trend_status[1]['key_level_index']
            else:
                ob_index = key_level.get('ob_correction_index')
    if signal_code[0] in ['68', 'prebot', 'bot']:
        ob_index = key_level['key_level_index']
    if ob_index and (df.loc[ob_index][high] - df.loc[waves[-1]['checked_index']][low])*direction > 0:
        ob_index = None
    if ob_index is None:
        if signal_code[0] == 'below':
            ob_index = key_level.get('ob_correction_index')
        else:
            ob_index = key_level['key_level_index']
    return ob_index
    # if key_level['label'] == 1:
    #     return ob_index, df.loc[ob_index]['Low'] - df.loc[key_level['checked_index']]['ma_candle'] * ratio_ma_sl
    # else:
    #     return ob_index, df.loc[ob_index]['High'] + df.loc[key_level['checked_index']]['ma_candle'] * ratio_ma_sl

def find_broken_correction_trend(old_trend_status, trend_status):
    main_trend_label = trend_status[0]['label']
    for index, trend in enumerate(old_trend_status):
        key_list = [d['key_level_index'] for d in trend_status]
        if trend['label'] != main_trend_label and trend['key_level_index'] not in key_list:
            if len(trend_status) > index:
                if trend_status[index]['label'] == main_trend_label:
                    return trend
            else:
                if trend_status[-1]['label'] == main_trend_label:
                    return trend
    return None

def find_broken_trend(old_trend_status, trend_status):
    for index, trend in enumerate(old_trend_status):
        if index > len(trend_status) - 1 or trend['label'] != trend_status[index]['label']:
            return trend
    return None


def find_entry(df, waves, signal_code, old_trend_status, trend_status, sl, tp):
    entry_price = df.loc[waves[-1]['checked_index'], 'Close']
    es_rr = cal_rr(entry_price, sl, tp)
    if coc_pattern_point[signal_code[1]]['entry_now'] or es_rr > min_rr_open_now:
        return (entry_price, sl, tp, es_rr)
    if trend_status[0]['label'] == 1:
        direction = 1
        peak = 'High'
    else:
        direction = -1
        peak = 'Low'
    if (sl - df.loc[trend_status[0]['checked_index'], 'Close'])*direction > 0:
        raise ValueError('sl invalid')
    if (tp - df.loc[trend_status[0]['checked_index'], 'Close'])*direction < 0:
        raise ValueError('tp invalid')
    if len(trend_status) == 1:
        entry_price = df.loc[trend_status[0]['key_level_index'], 'Open']
        es_rr = cal_rr(entry_price, sl, tp)
    else:
        entry_index = find_broken_correction_trend(old_trend_status, trend_status)
        if entry_index is None:
            entry_index =  trend_status[-1]['secondary_key_index']
            entry_price = df.loc[entry_index][peak]
        else:
            entry_index = entry_index['key_level_index']
            entry_price = df.loc[entry_index][peak] + df.loc[trend_status[0]['checked_index']]['ma_candle']*direction
        es_rr = cal_rr(entry_price, sl, tp)
    return (entry_price, sl, tp, es_rr)


def cal_rr(entry, sl, tp):
    if entry > tp and entry > sl or entry < tp and entry < sl:
        raise ValueError('Invalid input to cal RR')
    return abs(entry-tp)/abs(entry-sl)

def find_sl_or_tp_get_hit(name, low_tf, position):
    main_index = position['close_at']
    sl = position['sl']
    tp = position['tp']
    label = position['label']
    low_tf_df = df[name][low_tf].loc[main_index:]
    if label == 1:
        for index, row in low_tf_df.iterrows():
            is_hit_tp = row['High'] >= tp
            is_hit_sl = row['Low'] <= sl
            if is_hit_tp and is_hit_sl:
                return None
            if is_hit_tp:
                return 'tp'
            if is_hit_sl:
                return 'sl'
    else:
        for index, row in low_tf_df.iterrows():
            is_hit_tp = row['Low'] <= tp
            is_hit_sl = row['High'] >= sl
            if is_hit_tp and is_hit_sl:
                return None
            if is_hit_tp:
                return 'tp'
            if is_hit_sl:
                return 'sl'
    print('main index not hit any sl tp')
    return False

def open_position(name, main_tf, high_tf, very_high_tf, signal_code, point, entry, ob_sl, tf_sl, ob_tp, tf_tp, label=None):
    global df, waves, primary_level, trend_status, old_trend_status, fake_breakout_checking, break_index, position_status, pending_position, ratio_before, ratio_after, broken_trendline
    main_df = df[name][main_tf]
    if label is None:
        label = primary_level[name][main_tf]['label']
    if ob_sl is None:
        ob_sl = find_sl(main_df, waves[name][main_tf], primary_level[name][main_tf], signal_code, old_trend_status[name][main_tf], trend_status[name][main_tf])
        tf_sl = main_tf
    if label == 1:
        sl = df[name][tf_sl].loc[ob_sl]['Low'] - df[name][tf_sl].loc[primary_level[name][tf_sl]['checked_index']]['ma_candle'] * ratio_ma_sl
        while sl >= main_df.loc[waves[name][timeframe][-1]['checked_index'], 'Close']:
            sl = sl - df[name][tf_sl].loc[primary_level[name][tf_sl]['checked_index']]['ma_candle'] * ratio_ma_sl
    else:
        sl = df[name][tf_sl].loc[ob_sl]['High'] + df[name][tf_sl].loc[primary_level[name][tf_sl]['checked_index']]['ma_candle'] * ratio_ma_sl
        while sl <= main_df.loc[waves[name][timeframe][-1]['checked_index'], 'Close']:
            sl = sl + df[name][tf_sl].loc[primary_level[name][tf_sl]['checked_index']]['ma_candle'] * ratio_ma_sl
    if ob_tp is None:
        tf_tp = main_tf
        if label == primary_level[name][main_tf]['label']:
            ob_tp, tp = find_tp(main_df, waves[name][main_tf], primary_level[name][main_tf], signal_code, old_trend_status[name][main_tf], sl)
        else:
            # if label == primary_level[name][high_tf]['label']:
            ob_tp, tp = primary_level[name][high_tf]['peak_index'], df[name][high_tf].loc[primary_level[name][high_tf]['peak_index']]['Close']
    else:
        tp = df[name][tf_tp].loc[ob_tp]['Close']
    if entry is None:
        entry, sl, tp, rr = find_entry(main_df, waves[name][main_tf], signal_code, old_trend_status[name][main_tf], trend_status[name][main_tf], sl, tp)
    else:
        rr = cal_rr(entry, sl, tp)
    # if coc_pattern_point[signal_code[1]]['require_ob_tp'] and ob_tp is None:
    #     print('Skip signal because ob tp none')
    #     return
    if rr < min_rr:
        print('Skip signal not reach min rr')
        return
    if signal_code[1] == 'range_fake_breakout':
        fake_breakout_checking = False
        break_index = None
    if coc_pattern_point[signal_code[1]]['entry_now'] or entry == main_df.loc[waves[name][main_tf][-1]['checked_index'], 'Close']:
        init_status = 'open'
    else:
        init_status = 'pending'
    if coc_pattern_point[signal_code[1]]['reverse_mode']:
        t = sl
        sl = tp
        tp = t
        label = 1 - label
        rr = 1/rr
    # Open a position:
    position = {
        'signal_code': signal_code,
        'rr': rr,
        'label': label,
        'tp': tp,
        'sl': sl,
        'ob_sl': ob_sl,
        'tf_sl': tf_sl,
        'ob_tp': ob_tp,
        'tf_tp': tf_tp,
        'entry': entry,
        'entry_at': waves[name][main_tf][-1]['checked_index'],
        'status': init_status,
        'point': point,
        'open_at': None,
        'close_at': None,
        'success': None,
        'key_1_old': old_trend_status[name][timeframe][1]['key_level_index'] if len(old_trend_status[name][timeframe]) > 1 else None,
        'key_1_new': trend_status[name][timeframe][1]['key_level_index'] if len(trend_status[name][timeframe]) > 1 else None,
        'broken_trendline': broken_trendline,
    }
    if init_status == 'open':
        position_status[name][timeframe].append(position)
    else:
        pending_position[name][timeframe].append(position)
    print(f"New {init_status} position added")

def handle_trade(name, main_tf, high_tf, very_high_tf):
    global position_status, primary_level, df, waves, trend_status, closed_position, cancelled_position, pending_position
    current_position = position_status[name][main_tf]
    current_pending = pending_position[name][main_tf]
    latest_checked_index = waves[name][main_tf][-1]['checked_index']
    main_df = df[name][main_tf]
    # No pending or opening position
    # Check signal and point
    signal_code, point, entry_sl_tp = analyze_point(name, main_tf, high_tf, very_high_tf)
    # Condition open position
    if point and coc_pattern_point[signal_code[1]]['max_open_trade'] > len(current_pending) + len(current_position) and point >= min_point_open :
        entry, ob_sl, tf_sl, ob_tp, tf_tp, label = entry_sl_tp if entry_sl_tp else (None,) * 6
        open_position(name, main_tf, high_tf, very_high_tf, signal_code, point, entry, ob_sl, tf_sl, ob_tp, tf_tp, label)
    # Check if any pending position to update entry if new signal poin >= the old one
    if current_pending:
        # Fist check pending position has opened?
        if current_pending[-1]['label'] == 1:
            is_open = main_df.loc[latest_checked_index]['Low'] <= current_pending[-1]['entry']
        else:
            is_open = main_df.loc[latest_checked_index]['High'] >= current_pending[-1]['entry']
        if is_open:
            current_pending[-1]['status'] = 'open'
            current_pending[-1]['open_at'] = latest_checked_index
            current_position.append(current_pending.pop())
            print('Pending position has opened')
        else:
            # Cancel pending position if it went too far / hit tp without opening
            is_hit_tp = False
            if current_pending[-1]['label'] == 1:
                is_hit_tp = main_df.loc[latest_checked_index]['High'] >= current_pending[-1]['tp']
            else:
                is_hit_tp = main_df.loc[latest_checked_index]['Low'] <= current_pending[-1]['tp']
            if is_hit_tp and is_confirm_break(main_df, latest_checked_index, current_pending[-1]['tp'], current_pending[-1]['label'], current_pending[-1]['entry_at']):
                print('Pending order cancel due to break tp without opening')
                current_pending[-1]['status'] = 'cancelled'
                cancelled_position[name][main_tf].append(current_pending.pop())

            # If current pending not open yet but new signal come
            if len(current_pending) > 0:
                if point and point > current_pending[-1]['point']:
                    current_pending.pop()
                    entry, ob_sl, tf_sl, ob_tp, tf_tp, label = entry_sl_tp if entry_sl_tp else (None,) * 6
                    open_position(name, main_tf, high_tf, very_high_tf, signal_code, point, entry, ob_sl, tf_sl, ob_tp, tf_tp, label)

    for position in current_position:
        if position['status'] == 'open':
            # Check position close?
            if position['label'] == 1:
                is_hit_sl = main_df.loc[latest_checked_index]['Low'] <= position['sl']
                is_hit_tp = main_df.loc[latest_checked_index]['High'] >= position['tp']
            else:
                is_hit_sl = main_df.loc[latest_checked_index]['High'] >= position['sl']
                is_hit_tp = main_df.loc[latest_checked_index]['Low'] <= position['tp']
            if is_hit_sl or is_hit_tp:
                position['status'] = 'close'
                position['close_at'] = latest_checked_index
                if position['close_at'] == position['open_at']:
                    # Look into small tf to know hit tp or sl first
                    result = find_sl_or_tp_get_hit(name, low_tf, position) if low_tf else None
                    if result == None or result == False:
                        position['success'] = 'undefined'
                    else:
                        position['success'] = result == 'tp'
                else:
                    position['success'] = is_hit_tp
                closed_position[name][main_tf].append(copy.deepcopy(position))
                current_position.remove(position)
                print(f"Open position closed with success {is_hit_tp}")



def plot_waves_and_level(name, timeframe, key_level, number_waves_before=5, number_waves_after=0):
    global waves
    lines = []
    index_wave_entry = find_wave_index_candle_belongs_to(key_level['secondary_key_index'], waves[name][timeframe])
    index_wave_entry = index_wave_entry - number_waves_before
    if index_wave_entry*-1 >= len(waves[name][timeframe]):
        index_wave_entry = None
    draw_waves = waves[name][timeframe][index_wave_entry:]
    for wave in draw_waves:
        start_date = wave['start_index']
        end_date = wave['peak_index']
        start_close = df[name][timeframe].loc[start_date]['Close']
        end_close = df[name][timeframe].loc[end_date]['Close']
        lines.append([(start_date, start_close), (end_date, end_close)])

    draw_df = df[name][timeframe].loc[draw_waves[0]['start_index']:draw_waves[-1]['checked_index']]
    if key_level['label'] == 1:
        color_level = (0, 1, 0, 0.1)
    else:
        color_level = (1, 0, 0, 0.1)

    where_values = df[name][timeframe].loc[key_level['key_level_index']:key_level['checked_index']].index
    where_values = draw_df.index.isin(where_values)

    where_values_secondary = df[name][timeframe].loc[key_level['secondary_key_index']:key_level['checked_index']].index
    where_values_secondary = draw_df.index.isin(where_values_secondary)

    fill_color_dic_primary = dict(
        y1=df[name][timeframe].loc[key_level['key_level_index']]['Low'],
        y2=df[name][timeframe].loc[key_level['key_level_index']]['High'],
        color=color_level,
        where=where_values
    )

    fill_color_dic_secondary = dict(
        y1=df[name][timeframe].loc[key_level['secondary_key_index']]['Low'],
        y2=df[name][timeframe].loc[key_level['secondary_key_index']]['High'],
        color=(1, 1, 0, 0.1),  # Adjust the color as needed
        where=where_values_secondary
    )

    fill_color_list = [fill_color_dic_primary, fill_color_dic_secondary]

    if key_level['old_key_level_index']:
        tlines_params = dict(tlines=[(key_level['old_key_level_index'], key_level['key_level_index'])], tline_use='Close', colors='black', linestyle='-.', alpha=0.35)
    else:
        tlines_params = None

    apds = [
        # mpf.make_addplot(draw_df['tenkan'], color='blue'),
        # mpf.make_addplot(draw_df['kijun'], color='red'),
        # mpf.make_addplot(draw_df['span_a'], color='green', alpha=0.5),
        # mpf.make_addplot(draw_df['span_b'], color='orange', alpha=0.5),
        # mpf.make_addplot(draw_df['chikou'], color='purple'),
    ]

    # Plot the candlestick chart with waves using 'charles' style
    if tlines_params is not None:
        mpf.plot(draw_df, type='candle',
                addplot=apds,
                alines=dict(alines=lines,
                colors=['b','r','c','k','g'],alpha=[0.35],linewidths=[1.5]),
                tlines=tlines_params,
                volume=True,
                style='charles',
                title='Candlestick chart with waves',
                fill_between=fill_color_list,
                warn_too_much_data=10000000000,
        )
    else:
        mpf.plot(draw_df, type='candle',
                addplot=apds,
                alines=dict(alines=lines,
                colors=['b','r','c','k','g'],alpha=[0.35],linewidths=[1.5]),
                volume=True,
                style='charles',
                title='Candlestick chart with waves',
                fill_between=fill_color_list,
                warn_too_much_data=10000000000,
        )

def find_row_other_tf(name, main_row, other_tf):
    global df
    find_row_other = df[name][other_tf].loc[:main_row.name]
    if len(find_row_other) == 0:
        return None
    return find_row_other.iloc[-1]

def next_row(name, main_row, main_tf, low_tf, high_tf):
    global df
    index = df[name][main_tf].index.get_loc(main_row.name)
    next_main_row =  df[name][main_tf].iloc[index + 1]
    next_low_row = find_row_other_tf(name, next_main_row, low_tf)
    next_high_row = find_row_other_tf(name, next_main_row, high_tf)
    return (next_main_row, next_low_row, next_high_row)

def row_to_number_row(name, tf, row):
    if row is None:
        return
    return df[name][tf].index.get_loc(row.name) + 1

def update_level_and_status_all(name, main_tf, low_tf, high_tf, number_row):
    global df
    row = update_level_and_status_to_row_number(name, main_tf, number_row)
    row_low = update_level_and_status_to_row_number(name, low_tf, row_to_number_row(name, low_tf, find_row_other_tf(name, row, low_tf)))
    row_high = update_level_and_status_to_row_number(name, high_tf, row_to_number_row(name, high_tf, find_row_other_tf(name, row, high_tf)))
    return (row, row_low, row_high)

def plot_current_position(current_position, name, timeframe, number_waves=10):
    global waves, primary_level, position_status
    local_waves = waves
    key_level = primary_level[name][timeframe]
    if len(current_position) == 0:
        return

    current_position = current_position[-1]
    lines = []
    present_obs = [current_position['ob_sl'], key_level['key_level_index']]
    if current_position['ob_tp']:
        present_obs.append(current_position['ob_tp'])
    if current_position['broken_trendline']:
        present_obs.append(current_position['broken_trendline']['start_index'])
    index_wave_entry = find_wave_index_candle_belongs_to(min(present_obs), waves[name][timeframe])
    if index_wave_entry != None:
        index_wave_entry = index_wave_entry-number_waves
    index_wave_close = None
    if current_position['close_at']:
        index_wave_close = find_wave_index_candle_belongs_to(current_position['close_at'], local_waves[name][timeframe])
    if index_wave_close and index_wave_close < -1:
        draw_waves = local_waves[name][timeframe][index_wave_entry:index_wave_close+1]
    else:
        if index_wave_entry != None:
            draw_waves = local_waves[name][timeframe][index_wave_entry:]
        else:
            draw_waves = local_waves[name][timeframe]
    for wave in draw_waves:
        start_date = wave['start_index']
        end_date = wave['peak_index']
        start_close = df[name][timeframe].loc[start_date]['Close']
        end_close = df[name][timeframe].loc[end_date]['Close']
        lines.append([(start_date, start_close), (end_date, end_close)])

    draw_df = df[name][timeframe].loc[draw_waves[0]['start_index']:draw_waves[-1]['checked_index']]
    if key_level['label'] == 1:
        color_level = (0, 1, 0, 0.1)
    else:
        color_level = (1, 0, 0, 0.1)

    where_values = df[name][timeframe].loc[key_level['key_level_index']:key_level['checked_index']].index
    where_values = draw_df.index.isin(where_values)

    where_values_secondary = df[name][timeframe].loc[key_level['secondary_key_index']:key_level['checked_index']].index
    where_values_secondary = draw_df.index.isin(where_values_secondary)

    where_ob_sl = df[name][timeframe].loc[current_position['ob_sl']:key_level['checked_index']].index

    fill_color_dic_primary = dict(
        y1=df[name][timeframe].loc[key_level['key_level_index']]['Low'],
        y2=df[name][timeframe].loc[key_level['key_level_index']]['High'],
        color=color_level,
        where=where_values
    )

    fill_color_dic_secondary = dict(
        y1=df[name][timeframe].loc[key_level['secondary_key_index']]['Low'],
        y2=df[name][timeframe].loc[key_level['secondary_key_index']]['High'],
        color=(1, 1, 0, 0.1),  # Adjust the color as needed
        where=where_values_secondary
    )

    fill_color_dic_ob_sl = dict(
            y1=df[name][current_position['tf_sl']].loc[current_position['ob_sl']]['Open'],
            y2=df[name][current_position['tf_sl']].loc[current_position['ob_sl']]['Close'],
            color='#FFA50080',  # Orrange
            where=draw_df.index.isin(where_ob_sl)
        )

    fill_color_list = [fill_color_dic_primary, fill_color_dic_secondary, fill_color_dic_ob_sl]

    if current_position['ob_tp']:
        where_ob_tp = df[name][timeframe].loc[current_position['ob_tp']:key_level['checked_index']].index

        fill_color_dic_ob_tp = dict(
            y1=df[name][current_position['tf_tp']].loc[current_position['ob_tp']]['Open'],
            y2=df[name][current_position['tf_tp']].loc[current_position['ob_tp']]['Close'],
            color='#00FFFF80',  # Cyan
            where=draw_df.index.isin(where_ob_tp)
        )
        fill_color_list.append(fill_color_dic_ob_tp)

    # if current_position['key_1_old']:
    #     where_ob_old = df[name][timeframe].loc[current_position['key_1_old']:key_level['checked_index']].index

    #     fill_color_dic_ob_tp = dict(
    #         y1=df[name][timeframe].loc[current_position['key_1_old']]['Low'],
    #         y2=df[name][timeframe].loc[current_position['key_1_old']]['High'],
    #         color='#AA3FE880',  # Purple
    #         where=draw_df.index.isin(where_ob_old)
    #     )
    #     fill_color_list.append(fill_color_dic_ob_tp)

    # if current_position['key_1_new']:
    #     where_ob_new = df[name][timeframe].loc[current_position['key_1_new']:key_level['checked_index']].index

    #     fill_color_dic_ob_tp = dict(
    #         y1=df[name][timeframe].loc[current_position['key_1_new']]['Low'],
    #         y2=df[name][timeframe].loc[current_position['key_1_new']]['High'],
    #         color='#FF45C080',  # Pink
    #         where=draw_df.index.isin(where_ob_new)
    #     )
    #     fill_color_list.append(fill_color_dic_ob_tp)

    if current_position['broken_trendline']:
        tlines_params = dict(tlines=[(current_position['broken_trendline']['start_index'], current_position['broken_trendline']['end_index'])], tline_use=['Close'], colors='black', linestyle='-.')
    else:
        tlines_params = None

    # Plot the candlestick chart with waves using 'charles' style
    if tlines_params:
        mpf.plot(draw_df, type='candle',
                alines=dict(alines=lines,
                colors=['b','r','c','k','g'], alpha=[0.35],linewidths=[1]),
                tlines=tlines_params,
                volume=True,
                hlines=dict(hlines=[current_position['tp'],current_position['sl'], current_position['entry']],colors=['g','r','black'], linestyle='-.'),
                vlines=dict(vlines=[current_position['entry_at']], colors=['black'], linestyle='-.'),
                style='charles',
                title='Candlestick chart with waves',
                fill_between=fill_color_list,
                warn_too_much_data=10000000000
        )
    else:
        mpf.plot(draw_df, type='candle',
                alines=dict(alines=lines,
                colors=['b','r','c','k','g'],alpha=[0.35],linewidths=[1]),
                volume=True,
                hlines=dict(hlines=[current_position['tp'],current_position['sl'], current_position['entry']],colors=['g','r','black'], linestyle='-.'),
                vlines=dict(vlines=[current_position['entry_at']], colors=['black'], linestyle='-.'),
                style='charles',
                title='Candlestick chart with waves',
                fill_between=fill_color_list,
                warn_too_much_data=10000000000
        )

name = 'BTCUSD'
timeframe = 15

row = update_level_and_status_to_row_number(name, timeframe, 1)
history = {
    'primary_level': [],
    'sub_primary_level': [],
    'recent_level': [],
}

plot_waves_and_level(name, timeframe, primary_level[name][timeframe], 10, 0)

# Check drawing level:
row = update_level_and_status_to_row_number(name, timeframe, 500)
plot_waves_and_level(name, timeframe, primary_level[name][timeframe], 10, 0)


main_index = df[name][timeframe].index.get_loc(row.name)
row =  df[name][timeframe].iloc[main_index + 1]
update_level_and_trend_status_with_new_row(name, timeframe, row)

old_history_size = len(history['primary_level'])
while True:
    main_index = df[name][timeframe].index.get_loc(row.name)
    row =  df[name][timeframe].iloc[main_index + 1]
    update_level_and_trend_status_with_new_row(name, timeframe, row)
    if len(history['primary_level']) > old_history_size:
        break
plot_waves_and_level(name, timeframe, primary_level[name][timeframe], 20, 0)
plot_waves_and_level(name, timeframe, history['sub_primary_level'][-1], 20, 0)
plot_waves_and_level(name, timeframe, history['recent_level'][-1], 20, 0)


step = 1000
while step > 0:
    step = step - 1
    main_index = df[name][timeframe].index.get_loc(row.name)
    row =  df[name][timeframe].iloc[main_index + 1]
    update_level_and_trend_status_with_new_row(name, timeframe, row)

plot_waves_and_level(name, main_tf, primary_level, 10, 0)

row, row_low, row_high = next_row(name, row, main_tf, high_tf, very_high_tf)
update_level_and_trend_status_with_new_row(name, main_tf, row)
plot_waves_and_level(name, main_tf, primary_level, 10, 0)

primary_level[name][timeframe]

len(df[name][timeframe].loc[waves[name][timeframe][-2]['start_index']:waves[name][timeframe][-1]['checked_index']])

plot_waves_and_level(name, main_tf, recent_level, 10, 0)

# Check drawing level:
row_very_high = update_level_and_status_to_row_number(name, very_high_tf, 3690, True)
plot_waves_and_level(name, very_high_tf, primary_level, 10, 0)

waves[name][timeframe][-1]

recent_level[name][timeframe]

op_key


p_level = level_from_waves(df[name][timeframe], waves[name][timeframe][0:-2], True)
fake_primary_level = {
    name: {
        timeframe: p_level
    }
}
plot_waves_and_level(name, timeframe, fake_primary_level, 3, 0)

plot_waves_and_level(name, high_tf, primary_level, 5)

plot_waves_and_level(name, timeframe, primary_level, 5)

plot_waves_and_level(name, very_high_tf, primary_level, 30)