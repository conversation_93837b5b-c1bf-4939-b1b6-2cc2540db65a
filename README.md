# Trading Bot - Advanced Cryptocurrency Trading System

A sophisticated cryptocurrency trading bot that implements multi-timeframe technical analysis, wave pattern recognition, and Smart Money Concepts (SMC) for automated Bitcoin trading.

## Features

- **Multi-timeframe Analysis**: 1m, 5m, 15m, 1h, 1d timeframes
- **Wave Pattern Recognition**: Advanced wave detection with confirmation mechanisms
- **Smart Money Concepts**: Order blocks, imbalances, and liquidity analysis
- **Real-time Data**: Binance WebSocket integration for live market data
- **Optimized Backtesting**: Parallel processing for fast strategy testing
- **Memory Optimization**: Efficient data management to prevent memory issues
- **Chart Generation**: Automated chart export with trading signals
- **Multiple Strategies**: 10+ built-in trading patterns

## Project Structure

```
trading_bot/
├── __init__.py              # Package initialization
├── config.py                # Configuration settings
├── data_manager.py          # Data management with memory optimization
├── technical_analysis.py    # Technical analysis functions
├── wave_analysis.py         # Wave detection and analysis
├── level_analysis.py        # Support/resistance level analysis
├── strategy_engine.py       # Strategy pattern recognition
├── chart_generator.py       # Chart generation and export
├── binance_client.py        # Binance API and WebSocket client
├── trade_engine.py          # Trade engine for backtesting and live trading
└── main.py                  # Main application entry point

data/                        # Historical data storage
charts/                      # Generated chart exports
logs/                        # Application logs
backtest_results/           # Backtest result files
```

## Installation

1. **Clone the repository**:
```bash
git clone <repository-url>
cd trading_bot
```

2. **Install dependencies**:
```bash
pip install pandas numpy matplotlib mplfinance websocket-client requests
```

3. **Create required directories**:
```bash
mkdir -p data charts logs backtest_results
```

## Quick Start

### Live Trading Mode

Start the bot in live trading mode to connect to Binance and analyze real-time data:

```bash
python -m trading_bot.main --mode live --symbol BTCUSD
```

The live trading mode now uses the TradeEngine with `trade_mode="live"` for real-time trade management, including:
- Automatic trade entry based on strategy signals
- Real-time trade exit monitoring (stop loss and take profit)
- Live balance tracking
- Risk management per trade

### Backtest Mode

Run a backtest to evaluate strategy performance:

```bash
# Test all strategies
python -m trading_bot.main --mode backtest --symbol BTCUSD --days 30 --balance 10000 --risk 0.02

# Test specific strategy
python -m trading_bot.main --mode backtest --symbol BTCUSD --days 30 --strategy correction_broken

# Test BOS strategy only
python -m trading_bot.main --mode backtest --symbol BTCUSD --days 30 --strategy bos
```

## Backtest System Usage

### Basic Backtest

```python
from trading_bot import TradingBot

# Create bot instance
bot = TradingBot()

# Run backtest for all strategies
success = bot.run_backtest(
    symbol="BTCUSD",
    days_back=30,
    initial_balance=10000.0,
    risk_per_trade=0.02
)

# Run backtest for specific strategy
success = bot.run_backtest(
    symbol="BTCUSD",
    days_back=30,
    initial_balance=10000.0,
    risk_per_trade=0.02,
    strategy_filter="correction_broken"
)
```

### Advanced Backtest Configuration

```python
from trading_bot import TradeEngine, DataManager, StrategyEngine
from datetime import datetime, timedelta

# Initialize components
data_manager = DataManager()
strategy_engine = StrategyEngine(data_manager)
backtest_engine = TradeEngine(data_manager, strategy_engine, trade_mode="test")

# Configure backtest parameters
start_date = datetime(2024, 1, 1)
end_date = datetime(2024, 6, 1)

# For live trading mode
live_engine = TradeEngine(data_manager, strategy_engine, trade_mode="live")
live_engine.set_live_trading_params(balance=10000.0, risk_per_trade=0.02)

# Run backtest with custom parameters
result = backtest_engine.run_backtest(
    symbol="BTCUSD",
    start_date=start_date,
    end_date=end_date,
    main_tf=15,           # Main timeframe (15 minutes)
    high_tf=60,           # Higher timeframe (1 hour)
    very_high_tf=1440,    # Very high timeframe (1 day)
    initial_balance=10000.0,
    risk_per_trade=0.02,  # 2% risk per trade
    strategy_filter=None  # None = all strategies (parallel), or specify one strategy
)

# Print results
print(f"Total Trades: {result.total_trades}")
print(f"Win Rate: {result.win_rate:.2f}%")
print(f"Total Profit: ${result.total_profit:.2f}")
print(f"Max Drawdown: ${result.max_drawdown:.2f}")
print(f"Profit Factor: {result.profit_factor:.2f}")
```

### Available Strategies

The system includes the following built-in strategies:

- **correction_broken**: Trend continuation after correction
- **zanshin1**: Reversal pattern type 1
- **zanshin2**: Reversal pattern type 2
- **bos**: Break of Structure pattern
- **break_range_reverse**: Range breakout reversal
- **smc**: Smart Money Concepts pattern
- **confluent_key**: Confluent key level pattern
- **range_fake_breakout**: Fake breakout pattern

```python
# Get list of available strategies
from trading_bot import StrategyEngine, DataManager

data_manager = DataManager()
strategy_engine = StrategyEngine(data_manager)
available_strategies = strategy_engine.get_available_strategies()
print("Available strategies:", available_strategies)
```

### Strategy-Specific Backtesting

```python
# Test only BOS strategy
result = backtest_engine.run_backtest(
    symbol="BTCUSD",
    start_date=start_date,
    end_date=end_date,
    strategy_filter="bos"
)

# Test only SMC strategy
result = backtest_engine.run_backtest(
    symbol="BTCUSD",
    start_date=start_date,
    end_date=end_date,
    strategy_filter="smc"
)
```

### Backtest Execution Modes

The trade engine automatically chooses the optimal execution mode:

#### **Single Strategy Mode** (when `--strategy` is specified)
- Uses **sequential processing** for maximum accuracy
- Processes data chronologically to maintain proper market conditions
- Ideal for strategy development and debugging

#### **All Strategies Mode** (when no `--strategy` specified)
- Uses **parallel strategy processing** for maximum speed
- Splits data into chunks, processes all strategies in parallel per chunk
- Each strategy runs independently with its own trade management
- Combines results from all strategies at the end

### Backtest Performance Optimization

The trade engine includes several optimizations:

1. **Smart Parallel Processing**:
   - Single strategy: Sequential processing for accuracy
   - All strategies: Parallel strategy processing per data chunk
2. **Memory Management**: Limits data size to prevent memory issues
3. **Chunked Processing**: Processes data in manageable chunks
4. **Strategy Filtering**: Test specific strategies for faster iteration
5. **Concurrent Trade Management**: Configurable max trades per strategy

### Backtest Results Analysis

```python
# Save results for later analysis
backtest_engine.save_results(result, "my_backtest_results.pkl")

# Load previous results
loaded_result = backtest_engine.load_results("my_backtest_results.pkl")

# Generate performance charts
from trading_bot import ChartGenerator

chart_generator = ChartGenerator(data_manager)
chart_path = chart_generator.create_backtest_summary_chart([result.to_dict()])
print(f"Chart saved to: {chart_path}")
```

## Strategy Development

### Adding New Strategies

The strategy engine uses a standardized format for easy development:

```python
from trading_bot.strategy_engine import StrategyResult

def my_custom_strategy(self, name: str, main_tf: int, high_tf: int, very_high_tf: int) -> Optional[StrategyResult]:
    """Custom strategy implementation"""
    try:
        # Get market data
        df = self.data_manager.get_dataframe(name, main_tf)
        waves = self.data_manager.waves[name][main_tf]
        primary_level = self.data_manager.primary_level[name][main_tf]

        # Implement your strategy logic here
        if your_entry_condition():
            # Calculate entry, stop loss, take profit
            entry_price = calculate_entry()
            stop_loss = calculate_stop_loss()
            take_profit = calculate_take_profit()

            # Calculate signal strength
            signal_strength = calculate_signal_strength()

            return StrategyResult(
                pattern_name='my_custom_pattern',
                signal_strength=signal_strength,
                entry_price=entry_price,
                stop_loss=stop_loss,
                take_profit=take_profit,
                risk_reward=abs(take_profit - entry_price) / abs(entry_price - stop_loss),
                entry_now=True,
                metadata={'custom_data': 'value'}
            )

        return None

    except Exception as e:
        logger.error(f"Error in custom strategy: {e}")
        return None

# Register the strategy
strategy_engine.strategies['my_custom_pattern'] = my_custom_strategy
```

### Strategy Input/Output Format

All strategies follow the standardized `StrategyResult` format:

**Input Parameters**:
- `name`: Symbol name (e.g., "BTCUSD")
- `main_tf`: Main timeframe for analysis
- `high_tf`: Higher timeframe for confluence
- `very_high_tf`: Very high timeframe for trend context

**Output Format**:
```python
StrategyResult(
    pattern_name="strategy_name",      # Strategy identifier
    signal_strength=4.0,               # Signal strength (0-10)
    entry_price=50000.0,               # Entry price
    stop_loss=49500.0,                 # Stop loss price
    take_profit=51000.0,               # Take profit price
    risk_reward=2.0,                   # Risk-reward ratio
    entry_now=True,                    # Whether to enter immediately
    metadata={}                        # Additional strategy data
)
```

## Configuration

### Key Configuration Parameters

Edit `config.py` to customize the bot behavior:

```python
# Trading parameters
TIMEFRAMES = [1, 5, 15, 60, 1440]
MIN_RR = 1                    # Minimum risk-reward ratio
MIN_RR_OPEN_NOW = 2.5        # Minimum RR for immediate entry

# Memory optimization
MAX_ROW_DF = 10000           # Maximum rows per dataframe
MAX_HISTORY_SIZE = 1000      # Maximum history entries
CLEANUP_INTERVAL = 100       # Cleanup frequency

# Backtest settings
MAX_CONCURRENT_TRADES_PER_STRATEGY = 1  # Max concurrent trades per strategy

# Technical analysis
BIG_CANDLE_RATIO = 2.2       # Big candle threshold
EG_CANDLE_RATIO = 80.0       # Engulfing candle threshold
WEAK_TREND_RATIO = 0.2       # Weak trend threshold
```

### Symbol Configuration

Add new symbols in `config.py`:

```python
CHECK_SYMBOLS = {
    'BTCUSD': None,
    'ETHUSD': None,
    'ADAUSD': None,
}
```

## Data Management

### Historical Data

The bot automatically fetches historical data from Binance. To manually fetch data:

```python
from trading_bot import BinanceDataFetcher

fetcher = BinanceDataFetcher()
klines = fetcher.fetch_historical_klines("BTCUSDT", "1m", 1000)
fetcher.save_to_csv(klines, "BTCUSD_1.csv")
```

### Memory Optimization

The system includes several memory optimization features:

- **Automatic cleanup**: Removes old data periodically
- **Data limits**: Limits the amount of data kept in memory
- **Efficient storage**: Uses optimized data structures
- **Garbage collection**: Forces garbage collection during cleanup

## Chart Generation

### Generate Trading Charts

```python
from trading_bot import ChartGenerator, DataManager

data_manager = DataManager()
chart_generator = ChartGenerator(data_manager)

# Plot waves and levels
chart_path = chart_generator.plot_waves_and_level(
    name="BTCUSD",
    timeframe=15,
    level=primary_level,
    num_candles=100
)

# Plot position analysis
chart_path = chart_generator.plot_position_analysis(
    name="BTCUSD",
    timeframe=15,
    position=position_data,
    num_candles=200
)
```

### Chart Customization

Charts are automatically saved to the `charts/` directory with timestamps. Customize chart settings in `config.py`:

```python
CHART_WIDTH = 1920
CHART_HEIGHT = 1080
CHART_DPI = 100
```

## Logging

The bot uses comprehensive logging for monitoring and debugging:

- **Console output**: Real-time status updates
- **File logging**: Detailed logs saved to `logs/trading_bot.log`
- **Error tracking**: Detailed error messages with stack traces
- **Performance monitoring**: Memory usage and processing time logs

## Performance Tips

1. **Use parallel backtesting** for large datasets
2. **Limit data size** using MAX_ROW_DF setting
3. **Regular cleanup** with appropriate CLEANUP_INTERVAL
4. **Monitor memory usage** through built-in logging
5. **Use appropriate timeframes** for your strategy

## Troubleshooting

### Common Issues

1. **Memory errors**: Reduce MAX_ROW_DF or increase CLEANUP_INTERVAL
2. **WebSocket disconnections**: Check internet connection and Binance API status
3. **Missing data**: Ensure CSV files are in the correct format in `data/` directory
4. **Strategy errors**: Check logs for detailed error messages

### Debug Mode

Enable debug logging by modifying the logging level in `main.py`:

```python
logging.basicConfig(level=logging.DEBUG)
```

## Contributing

1. Follow the standardized `StrategyResult` format for new strategies
2. Add comprehensive error handling and logging
3. Include unit tests for new functionality
4. Update documentation for new features

## License

This project is licensed under the MIT License.

## Disclaimer

This trading bot is for educational and research purposes only. Cryptocurrency trading involves significant risk, and you should never trade with money you cannot afford to lose. The authors are not responsible for any financial losses incurred through the use of this software.
