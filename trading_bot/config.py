"""
Configuration settings for the trading bot
"""

# Trading parameters
TIMEFRAMES = [1, 5, 15, 60, 1440]
CHECK_SYMBOLS = {
    'BTCUSD': None,
    # 'XAUUSD': 41,
}

# Technical analysis parameters
NUMBER_CAL_MA_CANDLE = 49
MINIMUM_IMBALANCE_RATIO = 1
BIG_CANDLE_RATIO = 2.2
EG_CANDLE_RATIO = 80.0
DOJI_CANDLE_RATIO = 11.0
NUMBER_CANDLES_SIDEWAY = 5
MIN_RR = 1
MIN_POINT_OPEN = 4
RATIO_MA_TP = 1
RATIO_MA_SL = 3
SMALL_CANDLE_RATIO = 1/3
PERCENT_DEFAULT_TP = 0.5
MIN_RR_OPEN_NOW = 2.5
SPACE_BREAK_RATIO = 0.1
LEVEL_BREAK_SPACE_RATIO = 0.5
MAX_CONFIRM_BREAK_RATIO = 11
WEAK_TREND_RATIO = 0.2
LIQUIDITY_RATIO = 1

# Memory optimization
MAX_ROW_DF = 10000  # Reduced from 100000
MAX_HISTORY_SIZE = 1000  # Limit history size
C<PERSON><PERSON>UP_INTERVAL = 100  # Clean up every N candles

# Backtest settings
MAX_CONCURRENT_TRADES_PER_STRATEGY = 1  # Max concurrent trades per strategy

# Binance API settings
BINANCE_BASE_URL = "https://api.binance.com"
WEBSOCKET_URL = "wss://stream.binance.com:9443/ws/"

# File paths
DATA_DIR = "data"
CHARTS_DIR = "charts"
LOGS_DIR = "logs"

# Chart settings
CHART_WIDTH = 1920
CHART_HEIGHT = 1080
CHART_DPI = 100

# Location and pattern points
LOCATION_POINT = {
    'top': 1,
    'bot': 2,
    'prebot': 1,
    'below': 3,
    'mid': 1,
    '68': 2,
    'secondary': 2,
}

CANDLES_PATTERN_POINT = {
    'eg': {'point': 4, 'count': 1},
    'mera': {'point': 3, 'count': 1},
    'osb': {'point': 2, 'count': 1},
    'nosd': {'point': 1, 'count': 2},
}

COC_PATTERN_POINT = {
    'correction_broken': {
        'location': ['bot', '68', 'below', 'prebot', 'secondary', 'mid', 'top'],
        'point': 4,
        'entry_now': True,
        'reverse_mode': False,
        'require_ob_tp': False,
        'candle_check': False,
    },
    'zanshin1': {
        'location': ['bot', '68', 'below', 'prebot', 'secondary', 'mid', 'top'],
        'point': 4,
        'entry_now': True,
        'reverse_mode': False,
        'require_ob_tp': False,
        'candle_check': False,
    },
    'zanshin2': {
        'location': ['bot', '68', 'below', 'prebot', 'secondary', 'mid', 'top'],
        'point': 4,
        'entry_now': True,
        'reverse_mode': False,
        'require_ob_tp': False,
        'candle_check': False,
        'max_open_trade': 1,
    },
    'bos': {
        'location': ['bot', '68', 'below', 'prebot'],
        'point': 4,
        'entry_now': True,
        'reverse_mode': False,
        'require_ob_tp': False,
        'candle_check': False,
        'max_open_trade': 1,
    },
    'break_range_reverse': {
        'location': ['bot', 'below', 'prebot'],
        'point': 4,
        'entry_now': True,
        'reverse_mode': False,
        'require_ob_tp': False,
        'candle_check': False,
        'max_open_trade': 1,
    },
    'smc': {
        'location': ['68'],
        'point': 4,
        'entry_now': True,
        'reverse_mode': False,
        'require_ob_tp': False,
        'candle_check': False,
        'max_open_trade': 1,
    },
    'confluent_key': {
        'location': ['68', 'bot', 'prebot'],
        'point': 4,
        'entry_now': True,
        'reverse_mode': False,
        'require_ob_tp': False,
        'candle_check': False,
        'max_open_trade': 1,
    },

    'range_fake_breakout': {
        'location': ['bot', '68', 'below', 'prebot'],
        'point': 4,
        'entry_now': True,
        'reverse_mode': False,
        'require_ob_tp': False,
        'candle_check': False,
    },
}
