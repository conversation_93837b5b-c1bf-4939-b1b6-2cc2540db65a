"""
Chart generation and visualization functions
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import mplfinance as mpf
from datetime import datetime
import os
from typing import Dict, List, Optional, Tuple, Any
import logging

from .config import *

logger = logging.getLogger(__name__)

class ChartGenerator:
    """Generate and export trading charts"""

    def __init__(self, data_manager):
        self.data_manager = data_manager

        # Create charts directory if it doesn't exist
        os.makedirs(CHARTS_DIR, exist_ok=True)

    def plot_waves_and_level(self, name: str, timeframe: int, level: Dict[str, Any],
                            num_candles: int = 100, position: Optional[Dict] = None,
                            save_path: Optional[str] = None) -> str:
        """
        Plot waves and levels with optional position

        Args:
            name: Symbol name
            timeframe: Timeframe
            level: Level data
            num_candles: Number of candles to show
            position: Position data (optional)
            save_path: Custom save path (optional)

        Returns:
            str: Path to saved chart
        """
        try:
            df = self.data_manager.get_dataframe(name, timeframe)
            waves = self.data_manager.waves[name][timeframe]

            if df is None or len(df) == 0:
                logger.error(f"No data available for {name} {timeframe}")
                return None

            # Prepare data for plotting
            draw_df = df.tail(num_candles).copy()

            # Create lines for waves
            lines = self._create_wave_lines(draw_df, waves, level)

            # Create fill areas for order blocks
            fill_color_list = self._create_fill_areas(draw_df, level, position)

            # Create horizontal lines for levels
            hlines_dict = self._create_horizontal_lines(level, position)

            # Create vertical lines for important events
            vlines_dict = self._create_vertical_lines(position)

            # Generate filename
            if save_path is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"{name}_{timeframe}_{timestamp}.png"
                save_path = os.path.join(CHARTS_DIR, filename)

            # Plot configuration
            plot_config = {
                'type': 'candle',
                'style': 'charles',
                'title': f'{name} {timeframe}min - Waves and Levels',
                'volume': True,
                'figsize': (CHART_WIDTH/CHART_DPI, CHART_HEIGHT/CHART_DPI),
                'savefig': dict(fname=save_path, dpi=CHART_DPI, bbox_inches='tight'),
                'warn_too_much_data': 10000000000
            }

            # Add lines if available
            if lines:
                plot_config['alines'] = dict(
                    alines=lines,
                    colors=['b', 'r', 'c', 'k', 'g'],
                    alpha=[0.35],
                    linewidths=[1]
                )

            # Add horizontal lines if available
            if hlines_dict:
                plot_config['hlines'] = hlines_dict

            # Add vertical lines if available
            if vlines_dict:
                plot_config['vlines'] = vlines_dict

            # Add fill areas if available
            if fill_color_list:
                plot_config['fill_between'] = fill_color_list

            # Generate the plot
            mpf.plot(draw_df, **plot_config)

            logger.info(f"Chart saved to: {save_path}")
            return save_path

        except Exception as e:
            logger.error(f"Error plotting chart: {e}")
            return None

    def plot_position_analysis(self, name: str, timeframe: int, position: Dict[str, Any],
                              num_candles: int = 200, save_path: Optional[str] = None) -> str:
        """
        Plot position analysis with entry, SL, TP

        Args:
            name: Symbol name
            timeframe: Timeframe
            position: Position data
            num_candles: Number of candles to show
            save_path: Custom save path (optional)

        Returns:
            str: Path to saved chart
        """
        try:
            df = self.data_manager.get_dataframe(name, timeframe)

            if df is None or len(df) == 0:
                logger.error(f"No data available for {name} {timeframe}")
                return None

            # Find position entry point in dataframe
            entry_index = None
            if 'entry_at' in position:
                try:
                    entry_index = df.index.get_loc(position['entry_at'])
                except KeyError:
                    entry_index = len(df) - num_candles
            else:
                entry_index = len(df) - num_candles

            # Get data around entry point
            start_index = max(0, entry_index - num_candles // 2)
            end_index = min(len(df), entry_index + num_candles // 2)
            draw_df = df.iloc[start_index:end_index].copy()

            # Generate filename
            if save_path is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"{name}_{timeframe}_position_{timestamp}.png"
                save_path = os.path.join(CHARTS_DIR, filename)

            # Create horizontal lines for position levels
            hlines = []
            colors = []
            linestyles = []

            if 'entry' in position:
                hlines.append(position['entry'])
                colors.append('blue')
                linestyles.append('-')

            if 'sl' in position:
                hlines.append(position['sl'])
                colors.append('red')
                linestyles.append('--')

            if 'tp' in position:
                hlines.append(position['tp'])
                colors.append('green')
                linestyles.append('--')

            # Create vertical line for entry time
            vlines = []
            if 'entry_at' in position and position['entry_at'] in draw_df.index:
                vlines.append(position['entry_at'])

            # Plot configuration
            plot_config = {
                'type': 'candle',
                'style': 'charles',
                'title': f'{name} {timeframe}min - Position Analysis',
                'volume': True,
                'figsize': (CHART_WIDTH/CHART_DPI, CHART_HEIGHT/CHART_DPI),
                'savefig': dict(fname=save_path, dpi=CHART_DPI, bbox_inches='tight'),
                'warn_too_much_data': 10000000000
            }

            # Add horizontal lines
            if hlines:
                plot_config['hlines'] = dict(
                    hlines=hlines,
                    colors=colors,
                    linestyle=linestyles
                )

            # Add vertical lines
            if vlines:
                plot_config['vlines'] = dict(
                    vlines=vlines,
                    colors=['black'],
                    linestyle=['-']
                )

            # Generate the plot
            mpf.plot(draw_df, **plot_config)

            logger.info(f"Position chart saved to: {save_path}")
            return save_path

        except Exception as e:
            logger.error(f"Error plotting position chart: {e}")
            return None

    def _create_wave_lines(self, draw_df: pd.DataFrame, waves: List[Dict], level: Dict) -> List[Tuple]:
        """Create lines for wave visualization"""
        try:
            lines = []

            # Add wave lines
            for i, wave in enumerate(waves[-5:]):  # Show last 5 waves
                try:
                    start_idx = wave['start_index']
                    peak_idx = wave['peak_index']

                    if start_idx in draw_df.index and peak_idx in draw_df.index:
                        lines.append((start_idx, peak_idx))
                except Exception as e:
                    logger.debug(f"Error adding wave line {i}: {e}")
                    continue

            # Add level lines
            if level:
                try:
                    if 'key_level_index' in level and 'peak_index' in level:
                        key_idx = level['key_level_index']
                        peak_idx = level['peak_index']

                        if key_idx in draw_df.index and peak_idx in draw_df.index:
                            lines.append((key_idx, peak_idx))
                except Exception as e:
                    logger.debug(f"Error adding level line: {e}")

            return lines

        except Exception as e:
            logger.error(f"Error creating wave lines: {e}")
            return []

    def _create_fill_areas(self, draw_df: pd.DataFrame, level: Dict, position: Optional[Dict]) -> List[Dict]:
        """Create fill areas for order blocks"""
        try:
            fill_color_list = []

            # Add order block fill areas
            if level and 'key_level_index' in level:
                try:
                    key_index = level['key_level_index']
                    if key_index in draw_df.index:
                        candle = draw_df.loc[key_index]

                        fill_color_dict = {
                            'y1': candle['Low'],
                            'y2': candle['High'],
                            'color': '#FFD70080',  # Gold with transparency
                            'where': draw_df.index >= key_index
                        }
                        fill_color_list.append(fill_color_dict)
                except Exception as e:
                    logger.debug(f"Error adding order block fill: {e}")

            return fill_color_list

        except Exception as e:
            logger.error(f"Error creating fill areas: {e}")
            return []

    def _create_horizontal_lines(self, level: Dict, position: Optional[Dict]) -> Optional[Dict]:
        """Create horizontal lines for levels and position"""
        try:
            hlines = []
            colors = []
            linestyles = []

            # Add position lines
            if position:
                if 'entry' in position:
                    hlines.append(position['entry'])
                    colors.append('blue')
                    linestyles.append('-')

                if 'sl' in position:
                    hlines.append(position['sl'])
                    colors.append('red')
                    linestyles.append('--')

                if 'tp' in position:
                    hlines.append(position['tp'])
                    colors.append('green')
                    linestyles.append('--')

            if hlines:
                return dict(
                    hlines=hlines,
                    colors=colors,
                    linestyle=linestyles
                )

            return None

        except Exception as e:
            logger.error(f"Error creating horizontal lines: {e}")
            return None

    def _create_vertical_lines(self, position: Optional[Dict]) -> Optional[Dict]:
        """Create vertical lines for important events"""
        try:
            vlines = []

            if position and 'entry_at' in position:
                vlines.append(position['entry_at'])

            if vlines:
                return dict(
                    vlines=vlines,
                    colors=['black'],
                    linestyle=['-']
                )

            return None

        except Exception as e:
            logger.error(f"Error creating vertical lines: {e}")
            return None

    def create_backtest_summary_chart(self, results: List[Dict], save_path: Optional[str] = None) -> str:
        """Create backtest summary chart"""
        try:
            if not results:
                logger.error("No backtest results to plot")
                return None

            # Generate filename
            if save_path is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"backtest_summary_{timestamp}.png"
                save_path = os.path.join(CHARTS_DIR, filename)

            # Create figure with subplots
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

            # Extract data
            profits = [r.get('profit', 0) for r in results]
            cumulative_profits = np.cumsum(profits)
            win_rates = [r.get('win_rate', 0) for r in results]
            trade_counts = [r.get('trade_count', 0) for r in results]

            # Plot 1: Cumulative P&L
            ax1.plot(cumulative_profits, 'b-', linewidth=2)
            ax1.set_title('Cumulative P&L')
            ax1.set_xlabel('Trade Number')
            ax1.set_ylabel('Cumulative Profit')
            ax1.grid(True, alpha=0.3)

            # Plot 2: Win Rate
            ax2.plot(win_rates, 'g-', linewidth=2)
            ax2.set_title('Win Rate Over Time')
            ax2.set_xlabel('Trade Number')
            ax2.set_ylabel('Win Rate (%)')
            ax2.grid(True, alpha=0.3)

            # Plot 3: Profit Distribution
            ax3.hist(profits, bins=20, alpha=0.7, color='purple')
            ax3.set_title('Profit Distribution')
            ax3.set_xlabel('Profit')
            ax3.set_ylabel('Frequency')
            ax3.grid(True, alpha=0.3)

            # Plot 4: Trade Count
            ax4.bar(range(len(trade_counts)), trade_counts, alpha=0.7, color='orange')
            ax4.set_title('Trades per Period')
            ax4.set_xlabel('Period')
            ax4.set_ylabel('Trade Count')
            ax4.grid(True, alpha=0.3)

            plt.tight_layout()
            plt.savefig(save_path, dpi=CHART_DPI, bbox_inches='tight')
            plt.close()

            logger.info(f"Backtest summary chart saved to: {save_path}")
            return save_path

        except Exception as e:
            logger.error(f"Error creating backtest summary chart: {e}")
            return None
